<template>
    <div class="camera_preview">
        <div class="camera_preview_head">
            <div class="camera_preview_head_img">
                <img src="@/assets/images/videoDigitalHuman/camera_preview_head_img_return.svg" alt="">
            </div>
            <span class="camera_preview_head_text">摄像头录制</span>
        </div>
        <div class="camera_preview_tip">
            为了获得最佳、最逼真的效果，我们建议上传使用高分辨率相机或智能手机录制的2分钟视频。如果您只是在测试产品，请随时使用网络摄像头提交30秒的录制。
        </div>
        <!-- 摄像头画面显示区域 -->
        <div class="camera_preview_video_container">
            <div
                class="camera_preview_video"
                :class="{ landscape: isLandscape, portrait: !isLandscape }"
                v-if="videoStream"
            >
                <video
                    ref="videoRef"
                    autoplay
                    playsinline
                    muted
                    :class="{ mirror: isMirror }"
                ></video>
                <!-- 新增预览中标签 -->
                <div class="camera_preview_video_label">预览中</div>
            </div>
            <div class="camera_preview_empty" v-else>
              <div class="camera_preview_empty_content">
                <div class="camera_preview_empty_img">
                  <img src="@/assets/images/videoDigitalHuman/camera_preview_empty.png" alt="">
                </div>
                <div class="camera_preview_empty_text">
                  请允许浏览器访问您的相机和麦克风，并刷新页面摄像头与麦克风权限设置教程
                </div>
              </div>

            </div>
        </div>
        
        <!-- 设置展示和设备信息 -->
         <div class="camera_preview_set_device">
            <el-select
                v-model="orientation"
                placeholder="选择屏幕方向"
                @change="onOrientationChange"
                size="medium"
                style="width: 302px;"
                append-to="#app"
                popper-class="camera_preview_set_device_popper"
            >
                <template #label="{ label }">
                    <span><img class="camera_preview_set_device_img" src="@/assets/images/videoDigitalHuman/camera_preview_set_screen.svg" alt="">{{ label }}</span>
                </template>
                <el-option label="横屏 16:9" :value="true" />
                <el-option label="竖屏 9:16" :value="false" />
            </el-select>
            <el-select
                v-model="selectedVideoDeviceId"
                placeholder="请选择摄像头设备"
                @change="onDeviceChange"
                size="medium"
                style="width: 304px;"
                append-to="#app"
                popper-class="camera_preview_set_device_popper"
                >
                <template #label="{ label }">
                    <span><img class="camera_preview_set_device_img" src="@/assets/images/videoDigitalHuman/camera_preview_set_camera.svg" alt="">{{ label }}: </span>
                </template>
                <el-option
                    v-for="device in videoDevices"
                    :key="device.deviceId"
                    :label="device.label || `摄像头设备（ID: ${device.deviceId}）`"
                    :value="device.deviceId"
                />
            </el-select> 
            <el-select
                v-model="selectedAudioDeviceId"
                placeholder="请选择麦克风设备"
                @change="onDeviceChange"
                size="medium"
                style="width:302px;"
                append-to="#app"
                popper-class="camera_preview_set_device_popper"
                >
                <template #label="{ label }">
                    <span><img class="camera_preview_set_device_img" src="@/assets/images/videoDigitalHuman/camera_preview_set_microphone.svg" alt="">{{ label }}: </span>
                </template>
                <el-option
                    v-for="device in audioDevices"
                    :key="device.deviceId"
                    :label="device.label || `麦克风设备（ID: ${device.deviceId}）`"
                    :value="device.deviceId"
                />
                </el-select>
         </div>
         <div class="camera_preview_set_speech" @click="set_speech">
            <button>
              选择演讲方式
            </button>
         </div>
    </div>
    <speechMethod ref="speech_method_ref"></speechMethod>
</template>
<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from "vue";
import speechMethod from '@/views/modules/videoDigitalHuman/camera_record/speech_method_dialog.vue'
let status = ref("");
let error = ref("");
let btnDisabled = ref(false);
let videoStream = ref(null); // 摄像头流
let videoDevices = ref([])
let audioDevices = ref([]);
let videoRef = ref(null); // video 标签引用
let isLandscape = ref(true); // 横屏(true)或竖屏(false)
let isMirror = ref(true); // 镜像投影开关
let orientation = ref(true); // true 横屏，false 竖屏
let selectedAudioDeviceId = ref("");
let selectedVideoDeviceId = ref("");
let speech_method_ref=ref(null)
// 停止并释放摄像头流
let stopVideoStream=()=>{
  if (videoStream.value) {
    videoStream.value.getTracks().forEach((track) => track.stop());
    videoStream.value = null;
  }
}
let updateDeviceList=async()=>{
  try {
    // 延迟100ms，确保设备信息更新
    await new Promise((resolve) => setTimeout(resolve, 100));
    let devices = await navigator.mediaDevices.enumerateDevices();
    videoDevices.value = devices.filter((d) => d.kind === "videoinput");
    audioDevices.value = devices.filter((d) => d.kind === "audioinput");
    // 设置默认选中第一个设备（如果未设置过）
    if (!selectedVideoDeviceId.value && videoDevices.value.length > 0) {
      selectedVideoDeviceId.value = videoDevices.value[0].deviceId;
    }
    if (!selectedAudioDeviceId.value && audioDevices.value.length > 0) {
      selectedAudioDeviceId.value = audioDevices.value[0].deviceId;
    }
  } catch (err) {
    error.value = "获取设备列表失败：" + err.message;
  }
}
let requestPermissions = async () => {
  error.value = "";
  status.value = "正在请求摄像头和麦克风权限...";
  btnDisabled.value = true;
  await nextTick();

  try {
    stopVideoStream();

    let constraints = {
      audio: selectedAudioDeviceId.value
        ? { deviceId: { exact: selectedAudioDeviceId.value } }
        : true,
      video: selectedVideoDeviceId.value
        ? { deviceId: { exact: selectedVideoDeviceId.value } }
        : true,
    };

    let stream = await navigator.mediaDevices.getUserMedia(constraints);
    videoStream.value = stream;

    await updateDeviceList();

    let hasVideo = videoDevices.value.length > 0;
    let hasAudio = audioDevices.value.length > 0;
    let devicesAvailable = hasVideo && hasAudio;

    if (!devicesAvailable) {
      error.value = !hasVideo && !hasAudio
        ? "未检测到摄像头和麦克风设备，请确认设备已连接且未被占用。"
        : !hasVideo
        ? "未检测到摄像头设备，请确认摄像头已连接且未被占用。"
        : "未检测到麦克风设备，请确认麦克风已连接且未被占用。";
      status.value = "";
      stopVideoStream();
    } else {
      status.value = "权限已授予，且摄像头和麦克风设备均可用";

      if (videoRef.value) {
        videoRef.value.srcObject = stream;
      }
    }
  } catch (err) {
    // alert("打开摄像头失败: " + err.message);
    status.value = "";
    stopVideoStream();
    if (
      err.name === "NotFoundError" ||
      err.message.includes("Requested device not found")
    ) {
      error.value =
        "权限申请失败：未找到可用的摄像头或麦克风设备。<br>请确认设备已正确连接且未被其他程序占用。";
    } else if (
      err.name === "NotAllowedError" ||
      err.name === "PermissionDeniedError"
    ) {
      error.value =
        "权限申请失败：用户拒绝了访问权限。请允许访问摄像头和麦克风。";
    } else {
      error.value = "权限申请失败：" + err.message;
    }
  } finally {
    btnDisabled.value = false;
  }
};

let onOrientationChange=(value)=>{
  isLandscape.value = value;
}
let onDeviceChange=()=>{
  status.value = "设备选择已更改，正在重新请求权限...";
  requestPermissions();
}
let isMediaDevicesSupported=()=>{
  return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
}
let set_speech=()=>{
  speech_method_ref.value.dialogDetailVisible=true
}
onMounted(()=>{
    if (!isMediaDevicesSupported()) {
        error.value =
        "当前浏览器不支持访问摄像头和麦克风的功能，请使用最新版本的Chrome、Edge或Firefox。";
        btnDisabled.value = true;
        return;
    }
    updateDeviceList();
    navigator.mediaDevices.addEventListener("devicechange", async () => {
        status.value = "设备发生变化，正在更新设备列表...";
        await updateDeviceList();
        await requestPermissions()
        status.value = "设备列表已更新";
    });
    requestPermissions()
    speech_method_ref.value.init()
})
onBeforeUnmount(() => {
  navigator.mediaDevices.removeEventListener("devicechange", onDeviceChange);
  stopVideoStream();
});
watch(videoStream, (newStream) => {
  if (videoRef.value) {
    videoRef.value.srcObject = newStream;
  }
});
</script>
<style lang="scss" scoped>
.camera_preview{
    width: 1005px;
    height: 926px;
    padding: 43px 34px 46px 31px;
    box-sizing: border-box;
    background: #FFFFFF;
    display: flex;
    flex-direction: column;
    .camera_preview_head{
        display: flex;
        align-items: center;
        margin-bottom: 45px;
        .camera_preview_head_img{
            width: 10px;
            height: 20px;
            justify-self: flex-start;
            cursor: pointer;
            img{
                width: 100%;
                height: 100%;
            }
        }
        .camera_preview_head_text{
            font-size: 24px;
            line-height: 22px;
            color: #000000;
            margin: 0 auto;
        }
    }
    .camera_preview_tip{
        padding: 24px 32px;
        width:100%;
        background: rgba(10, 175, 96, 0.1);
        font-size: 16px;
        line-height: 30px;
        color: #353D49;
    }
    /* 摄像头画面容器 */
    .camera_preview_video_container {
        margin-top: 20px;
        overflow: hidden;
        height: 492px; // 固定高度
        transition: width 0.3s ease;
        width:100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 20px;
        .camera_preview_video{
            position: relative; // 使内部绝对定位生效
            width:100%;// 横屏宽度
            border-radius: 4px;
            overflow: hidden;
            height: 100%;
            &.portrait {
                width: 256px; // 竖屏宽度
                height: 100%;
            }
            video {
                width: 100%;
                height: 100%;
                object-fit: cover;
                display: block;
                border-radius: inherit;
            }

            video.mirror {
                transform: scaleX(-1);
            }

            /* 预览中标签样式 */
            .camera_preview_video_label {
                position: absolute;
                top: 8px;
                right: 12px;
                width: 76px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: rgba(0, 0, 0, 0.4);
                color: #fff;
                font-size: 16px;
                padding: 8px 14px;
                border-radius: 6px;
                user-select: none;
                pointer-events: none;
                z-index: 1;
                font-weight: 300;
            }
        }
        .camera_preview_empty{
          width: 100%;
          height: 100%;
          box-sizing: border-box;
          background: #F9F9F9;
          border: 2px dashed rgba(0, 0, 0, 0.1);
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          .camera_preview_empty_content{
            display: flex;
            flex-direction: column;
            width: 308px;
            align-items: center;
            .camera_preview_empty_img{
              width: 70px;
              height: 70px;
              margin-bottom: 19px;
              img{
                width: 100%;
                height: 100%;
              }

            }
            .camera_preview_empty_text{
              text-align: center;
              font-size: 14px;
              line-height: 30px;
              color: rgba(0, 0, 0, 0.45);

            }
          }

        }
        
    }
    .camera_preview_set_device{
        display: flex;
        align-items: center;
        margin-bottom: 12px;
        .el-select{
            margin-right: 16px;
            border: none;
            box-shadow: none;
            ::v-deep(.el-select__wrapper){
                padding: 24px;
                background-color:#F9F9F9;
                border-radius: 0;
                border: none;
                box-shadow: none;
                .el-select__selection{
                  height: 30px;
                  .el-select__selected-item{
                    span{
                      display: flex;
                      align-items: center;
                      font-size: 16px;
                      line-height: 30px;
                      color: #000000;
                      .camera_preview_set_device_img{
                        margin-right: 7px;
                        width: 24px;
                        height: 24px;
                      }
                    }
                  }
                }
                .el-select__suffix{
                  margin-left: 18px;
                  .el-icon{
                    width: 12px;
                    height: 8px;
                    background-image: url('@/assets/images/videoDigitalHuman/camera_preview_set_device_arrow.svg');
                    background-repeat: no-repeat;
                    background-size: 100% 100%;
                    background-position: 0 0;
                    svg{
                      display: none;
                    }
                  }
                }
                &.is-focused{
                  box-shadow: none;
                }
            }
            &:last-child{
                margin-right: 0;
            }
        }
    }
    .camera_preview_set_speech{
      display: flex;
      justify-content: center;
      button{
        box-sizing: border-box;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 132px;
        height: 41px;
        background: #0AAF60;
        border: 1px solid #0AAF60;
        border-radius: 6px;
        font-size: 16px;
        color: #fff;
        cursor: pointer;
        border: none;
      }
    }

}
</style>
<style lang="scss">
.el-popper{
  &.camera_preview_set_device_popper{
    border-radius: 0;
    background-color: #F9F9F9;
    margin-top: -1px;
    border: none;
    .el-select-dropdown__list{
      padding: 12px;
      .el-select-dropdown__item{
        // 
        padding: 0 12px;
        height: fit-content;
        line-height: 0;
        margin-bottom: 8px;
        span{
          font-size: 16px;
          line-height: 30px;
          color: #000000;
        }
        &.is-selected{
          font-weight: normal;
        }
        &.is-hovering{
          background-color: #EFEFF1;
        }
        &:last-child{
          margin-bottom: 0;
        }

      }
    }
    .el-popper__arrow{
      display: none;
    }
  }
}

</style>