# 代码修改文档检查清单模板

## 修改基本信息
- **修改日期**: [填写修改日期]
- **修改人员**: [填写修改人员]
- **修改类型**: [新功能/问题修复/功能优化/重构/其他]

## 修改范围和目标
- **修改模块/组件**: [填写修改的模块或组件名称]
- **修改文件**: [列出所有需要修改的文件路径]
- **修改目标**: [详细描述修改的目标和预期结果]
- **优先级**: [高/中/低]

## 相关文档检查

### 已查阅文档
- [ ] [文档名称1] - [简要说明文档内容和相关性]
- [ ] [文档名称2] - [简要说明文档内容和相关性]
- [ ] [文档名称3] - [简要说明文档内容和相关性]

### 关键信息提取
- **功能实现原理**: [从文档中提取的关键实现原理]
- **已知问题和限制**: [从文档中提取的已知问题和限制]
- **修改历史**: [从文档中提取的相关修改历史]
- **依赖关系**: [从文档中提取的依赖关系]

## 修改计划

### 修改步骤
1. [步骤1描述]
2. [步骤2描述]
3. [步骤3描述]
4. [步骤4描述]

### 影响分析
- **直接影响**: [列出直接受影响的功能或组件]
- **间接影响**: [列出间接受影响的功能或组件]
- **风险点**: [列出可能的风险点和应对措施]

### 测试计划
- **单元测试**: [描述需要进行单元测试的内容]
- **集成测试**: [描述需要进行集成测试的内容]
- **手动测试**: [描述需要进行手动测试的内容]

## 修改实施记录

### 代码修改详情
- **文件1**: [文件路径]
  - [ ] 修改点1: [描述修改内容和原因]
  - [ ] 修改点2: [描述修改内容和原因]
  
- **文件2**: [文件路径]
  - [ ] 修改点1: [描述修改内容和原因]
  - [ ] 修改点2: [描述修改内容和原因]

### 添加的注释
- [ ] [文件路径]: [添加的注释内容]

### 修改验证
- [ ] [验证项1]: [验证结果]
- [ ] [验证项2]: [验证结果]
- [ ] [验证项3]: [验证结果]

## 修改后文档更新

### 需要更新的文档
- [ ] [文档名称1]: [需要更新的内容]
- [ ] [文档名称2]: [需要更新的内容]

### 需要创建的文档
- [ ] [文档名称1]: [文档内容和结构]
- [ ] [文档名称2]: [文档内容和结构]

## 检查清单

### 修改前检查
- [ ] 是否已确定修改范围和目标？
- [ ] 是否已搜索并阅读相关文档？
- [ ] 是否了解功能的实现原理和设计思路？
- [ ] 是否已知相关问题和限制？
- [ ] 是否了解之前的修改历史？
- [ ] 是否已制定详细的修改计划？
- [ ] 是否已考虑可能的影响和风险？
- [ ] 是否已制定测试计划？

### 修改中检查
- [ ] 修改是否符合项目的编码规范？
- [ ] 是否已添加必要的注释？
- [ ] 修改是否与现有设计保持一致？
- [ ] 是否已处理可能的边界情况？
- [ ] 是否已进行必要的错误处理？

### 修改后检查
- [ ] 修改是否已通过所有测试？
- [ ] 是否已创建或更新相关文档？
- [ ] 文档是否包含修改的内容、原因和影响？
- [ ] 文档是否已保存到 `docs` 文件夹中？
- [ ] 是否已通知相关人员修改完成？

## 备注
[填写任何额外的备注或注意事项]