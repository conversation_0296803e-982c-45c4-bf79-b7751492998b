<script setup>
import SubMenu from '../../../sub-menu/index.vue'
// import Logo from '../../../logo/index.vue'
//引入路由js
import { staticRouter, errorRouter } from "@/router/modules/staticRouter";
import { useRouter,useRoute } from 'vue-router'; // 引入useRouter
import { ArrowLeft } from '@element-plus/icons-vue'; // 引入Element Plus的返回图标

const router = useRouter(); // 初始化router
let route=useRoute()
// 返回上一页的方法
const goBack = () => {
  router.go(-1);
}

// const menuStore = useMenuStore()
// console.log('menuStore',menuStore)
// const themeStore = useThemeStore()
// const menuTheme = computed(() => themeStore.color.menu)
// const { displayedMenus, active, collapse } = storeToRefs(menuStore)
// console.log('displayedMenus',displayedMenus)

const routeArr = [
  {
    path: "/home",
    name: "home",
    id:0,
    type: 1,
    titleName: "工作台",
    icon: "home",
    component: () => import("@/views/modules/home/<USER>"),
    // component: () => import("@/layouts/indexAsync.vue"),
  },
  // {
  //   path: "/AIDubbing",
  //   name: "AIDubbing",
  //   id: 1,
  //   type: 1,
  //   titleName: "AI配音",
  //   meta: { title: '', fixedHeader: true, no_scroll: true },
  //   component: () => import("@/views/modules/AIDubbing/index.vue"),
  // },
  // {
  //   path: "/commercialDubbing",
  //   name: "commercialDubbing",
  //   id: 2,
  //   type: 1,
  //   titleName: "AI商配",
  //   meta: { title: '', fixedHeader: true, no_scroll: true },
  //   component: () => import("@/views/modules/commercialDubbing/index.vue"),
  // },
  {
      path: "/soundStore",
      name: 'soundStore',
      id: 3,
      type: 1,
      titleName: "音色商店",

      // icon: "home",
      //redirect: { name: 'redirect' },  //此选项，跳转到redirect路由，redirect路由会跳转到登录页，因为登录页没有meta.title，所以会跳转到404页面
      meta: { title: '',fixedHeader:true, },
      component: () => import("@/views/modules/soundStore/index.vue"),
  },
  // header_module:'dark'
  {
      path: "/realVoice",
      name: 'realVoice',
      id: 4,
      type: 1,
      titleName: "真人配音",

      // icon: "home",
      //redirect: { name: 'redirect' },  //此选项，跳转到redirect路由，redirect路由会跳转到登录页，因为登录页没有meta.title，所以会跳转到404页面
      meta: { title: '',fixedHeader:true,},
      component: () =>import("@/views/modules/realVoice/index.vue"),
  },
  {
      path: 'myWorks',
      name: 'myWorks',
      id: 5,
      type: 1,
      titleName: '我的空间',
      component: () => import('@/views/modules/mySpace/myWorks/index.vue')
  },
  {
        path: "/membership",
        name: "membership",
        id: 6,
        type: 1,
        titleName: "会员计划",
        icon: "Star",
        meta: { title: '' },
        component: () => import("@/views/account/membership.vue"),
    },
    {
      path: "/apiService",
      name: "apiService",
      id: 7,
      type: 1,
      titleName: "API服务",
      meta: { title: '', fixedHeader: true },
      component: () => import("@/views/modules/apiService/index.vue"),
  },
  {
      path: "/cloneService",
      name: "cloneService",
      id: 8,
      type: 1,
      titleName: "声音克隆",
      meta: { title: '', fixedHeader: true },
      component: () => import("@/views/modules/cloneService/index.vue"),
  },
]

</script>
<template>
  <div class="sidebar-classic-container flex flex_d-column">
    <!--  logo以及名称  -->
<!--    <Logo/>-->
    <!--    <div class="flex-item_f-1"></div>-->
    <el-scrollbar class="flex flex_j_c-center flex_a_i-center" :style="`var(${--gl-headbar-height})`">
      <div class="header-nav-container flex flex_a_i-center">
        <!-- 添加返回按钮 -->
        <!-- <div class="back-button flex flex_a_i-center" v-if="!route.meta.hideBack" @click="goBack">
          <el-icon><ArrowLeft /></el-icon>
          <span>返回</span>
        </div> -->
        
        <el-menu
            :style="`var(${--gl-headbar-height});overflow: hidden;border: none;`"
            mode="horizontal"
            menu-trigger="click"
            class="flex flex_a_i-center nav-menu"
            :ellipsis="false"
            :default-active="0"
            :background-color="`var(--gl-color)`"
            text-color="#414244"
            active-text-color="#fff"
            :unique-opened="true">

          <SubMenu
              v-for="(item,index) in routeArr"
              :index="String(index)"
              :key="item.id"
              :data="item"
              style="overflow: hidden;height:50px;"
          />

          <!--     有子菜单     -->
          <!--        <el-sub-menu :index="item.id + ''" v-if="item.children && item.children.length > 0" :key="item.path">-->
          <!--          <template #title>-->
          <!--            <el-icon>-->
          <!--              <Icon :icon="item.icon" />-->
          <!--            </el-icon>-->
          <!--            <span>{{ item.name }}</span>-->
          <!--          </template>-->
          <!--          <NavMenu :menus="item.children">-->
          <!--          </NavMenu>-->
          <!--        </el-sub-menu>-->
          <!-- 菜单（没有子级菜单）-->
          <!--        <el-menu-item :index="item.path" @click="clickHandle">-->
          <!--          <el-icon>-->
          <!--            <Icon :icon="item.icon" />-->
          <!--          </el-icon>-->
          <!--          <template #title>-->
          <!--            <span>{{ item.titleName }}</span>-->
          <!--          </template>-->
          <!--        </el-menu-item>-->
          <!--        </template>-->
        </el-menu>
      </div>
    </el-scrollbar>
  </div>
</template>

<style lang="scss" scoped>
.sidebar-classic-container {
  // z-index: 10; // todo: 忘记当初为什么会加这个属性
  //background-color: var(--gl-sidebar-background-color);
  //height:var(--gl-headbar-height);
  //background-color: var(--gl-color);
  // box-shadow: var(--el-box-shadow-light);
  //.el-menu:not(.el-menu--collapse) {
  //  //width: var(--gl-sidebar-classic-width); // todo: 侧边栏的宽度
  //  border: none;
  //}
  //::v-deep(.el-scrollbar__view){
  //  height:50px!important;
  //}
  //::v-deep(.el-menu--horizontal){
  //  height:50px!important;
  //}
  //style attribute {
  //  --el-menu-text-color: #414244;
  //  --el-menu-hover-text-color: #414244;
  //  --el-menu-bg-color: var(--gl-color);
  //  --el-menu-hover-bg-color: #f00;
  //  --el-menu-active-color: #fff;
  //  --el-menu-level: 1;
  //}
  
  .header-nav-container {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
  }
  
  /* 添加返回按钮样式 */
  .back-button {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #353D49;
    margin-right: 27px;
    // margin-left: -20px; /* 向左偏移20px */
    margin-top: -1px;
    cursor: pointer;
    height: 40px;
    display: flex;
    align-items: center;
    // line-height: 40px;
    // padding-top: 2px; /* 微调垂直位置 */
    
    .el-icon {
      margin-right: 0px;
      margin-top: -3px;
      font-size: 14px;
    }
    
    /* 移除悬停效果 */
    &:hover {
      color: #353D49; /* 与默认颜色保持一致 */
    }
  }
  
  .nav-menu {
    height: 40px;
  }
  
  ::v-deep(.el-menu--horizontal){
    height: 40px;
    line-height: 40px;
    background-color: rgba(255,255,255,0);
  }
  ::v-deep(.el-menu--horizontal>.el-menu-item){
    padding: 0 36px 0 0 !important;
    height: fit-content;
    margin-top: 2px;
    border: none;
    span{
      font-size: 16px;
      color: #353D49;
    }
  }

  ::v-deep(.el-menu--horizontal>.el-menu-item.is-active) {
    /* border-bottom: 2px solid var(--el-menu-active-color); */
    //color: var(--el-menu-active-color) !important;
    //border: none;
    background-color: rgba(255,255,255,0);
    color: #555!important;
  }

    ::v-deep(.el-menu--horizontal>.el-menu-item:hover){
      background-color: #fff;
      color: #555!important;
    }

  ::v-deep(.el-sub-menu),::v-deep(.el-sub-menu):hover{
    background-color: #fff;
  }


  //::v-deep(.el-menu--horizontal>.el-menu-item) {
  //  /* border-bottom: 2px solid var(--el-menu-active-color); */
  //  //color: var(--el-menu-active-color) !important;
  //  //border: none;
  //  //background-color: rgba(255,255,255,0);
  //  //color:#555555;
  //}
  ::v-deep(.el-menu) {
    //border: none;
    padding:0;
    //height: 50px;
    ::v-deep(.el-menu--horizontal){
      background-color: #006eff;
    }
  }

  ///deep/.el-menu .el-menu-item:hover{
  //  outline: 0 !important;
  //  color: #2E95FB !important;
  //  background: linear-gradient(270deg, #F2F7FC 0%, #FEFEFE 100%)!important;
  //}
  ///deep/.el-menu .el-menu-item.is-active {
  //  color: #2E95FB !important;
  //  background: linear-gradient(270deg, #F2F7FC 0%, #FEFEFE 100%)!important;
  //}
  //::v-deep(.el-submenu .el-submenu__title):hover {
  //  color: #2E95FB !important;
  //  background: linear-gradient(270deg, #F2F7FC 0%, #FEFEFE 100%)!important;
  //}





  //::v-deep(.el-menu) {
  //  border: none;
  //  //padding:0 20px;
  //  height:50px;
  //
  //  ::v-deep(.el-menu-item) {
  //    //padding-right: 20px;
  //    //display: flex;
  //    //align-items: center;
  //    //border-radius: 10px;
  //    //height:50px;
  //    border: none;
  //  }
  ////
  ////  ::v-deep(.el-sub-menu__title){
  ////    border: 0;
  ////  }
  ////
  ////  ::v-deep(.el-menu-item.is-active){
  ////    //background-color: var(--main-color) !important; /* 修改激活项的背景色 */
  ////    //color: #5d5d5f !important; /* 修改激活项的文本颜色 */
  ////    height:50px;
  ////    border: none;
  ////  }
  ////  ::v-deep(.el-menu-item:hover){
  ////    //background-color: var(--main-color);
  ////    //color:#5d5d5f;
  ////  }
  ////
  ////  ::v-deep(.el-menu-item), ::v-deep(.el-sub-menu), ::v-deep(.el-sub-menu__title) {
  ////    //display: block;
  ////    overflow: hidden;
  ////    //text-overflow:ellipsis;
  ////    white-space: nowrap;
  ////
  ////    //background-color: var(--main-color) !important; /* 修改激活项的背景色 */
  ////    //color: #5d5d5f !important; /* 修改激活项的文本颜色 */
  ////    height:50px;
  ////    //border-radius: 10px;
  ////
  ////    & > .el-sub-menu__icon-arrow {
  ////      //position: absolute;
  ////      //right: 8px;
  ////    }
  ////  }
  //}
}

</style>
<style lang="scss">
.dark{
  .sidebar-classic-container{
    .el-menu--horizontal>.el-menu-item{
      span{
        color: #fff;
      }
      &:hover{
        background-color: transparent!important;
        span{
          color: #18AD25;;
        }
       
      }
    }
  } 
}
</style>
