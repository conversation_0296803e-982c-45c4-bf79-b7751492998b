<template>
	<div class="right_operate">
		<div class="right_operate_drive">
			<!-- <button @click="get_data">获取数据</button>
			{{ right_operate_ref }}-- -->
			<div class="right_operate_drive_tabs">
				<div class="right_operate_drive_tabs_item" v-for="(item, index) in drive_tabs" :key="index"
					@click="change_tabs(item)" :class="current_tab == item.id ? 'current' : ''">{{ item.name }}</div>
			</div>
			<div class="right_operate_drive_content">
				<textCaptions ref="text_captions_ref" v-if="current_tab == 1"></textCaptions>
				<aduioCaptions ref="aduio_captions_ref" v-else></aduioCaptions>
			</div>
		</div>
	</div>
</template>
<script setup>
import { ref, reactive, nextTick, provide,onActivated,defineEmits,onMounted ,watch} from 'vue';
// 导入数字人状态管理store
import { useDigitalHumanStore } from '../../store/digitalHumanStore';

// 定义事件发射器
const emit = defineEmits(['subtitle-toggle', 'audio-data-loaded']);
import textCaptions from '@/views/modules/digitalHuman/components/right_operate/input_text/index.vue'
import aduioCaptions from '@/views/modules/digitalHuman/components/right_operate/input_aduio/index.vue'

// 初始化数字人状态管理store
const digitalHumanStore = useDigitalHumanStore();



// 🔧 新增：保存digital_human_right_option的数据
let lastDigitalHumanRightOption = ref({});
let text_captions_ref = ref(null)
let aduio_captions_ref = ref(null)
let right_operate_ref = ref(  {
//   "text_captions": {
//     "captions": {
//       "textInfo": "aa",
//       "open_captions": false
//     },
//     "choose_music": {
//       "current_music": {
//         "current_classify": "平静",
//         "bgm_url": "https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/material/背景音乐/平静/在432赫兹做梦.mp3",
//         "current_nav": 1,
//         "info": {
//           "id": 228,
//           "theme": "平静",
//           "musicName": "在432赫兹做梦",
//           "ossPath": "https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/material/背景音乐/平静/在432赫兹做梦.mp3",
//           "createTime": [
//             2025,
//             4,
//             7,
//             11,
//             9,
//             8
//           ],
//           "updateTime": [
//             2025,
//             4,
//             7,
//             11,
//             9,
//             8
//           ],
//           "fileExtension": " ",
//           "color": "rgb(229,186,215)",
//           "color1": "rgb(209,238,156)",
//           "color2": "rgb(154,124,207)",
//           "isSelected": false,
//           "isPlaying": false,
//           "time": "06:56",
//           "volume": 80,
//           "url": "https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/material/背景音乐/平静/在432赫兹做梦.mp3"
//         }
//       }
//     },
//     "choose_dub": {
//       "current_character": {
//         "character_id": "伏夏-慵懒",
//         "option_active": 1,
//         "selectetMycurrent": "",
//         "info": {
//           "bookmarkInfo": null,
//           "bookmarkTime": null,
//           "id": 11796,
//           "voiceName": "伏夏-慵懒",
//           "gender": "女",
//           "ageGroup": "青年",
//           "sceneCategory": "热门音色",
//           "emotionTags": "慵懒",
//           "platformNickname": "奇妙惬",
//           "voiceType": "精品",
//           "price": 1,
//           "recommendTags": "品质旁白",
//           "demoText": "\"哈喽，亲爱的朋友们！我是七宝呀。今天想和你们分享一段超惬意的午后时光。\r\n\r\n你们能想象吗？在一个阳光正好的午后，我偶然走进了一家隐藏在街角的咖啡馆。推开门的那一刻，咖啡的香气就像一只温柔的手，轻轻地把我拉了进去。店里的装修是那种复古又温馨的风格，木质的桌椅，暖黄色的灯光，仿佛时间都在这里慢了下来。\r\n\r\n我点了一杯手冲咖啡，看着咖啡师专注地操作着，那手法就像是在雕琢一件艺术品。当咖啡端上来时，浓郁的香气扑鼻而来。我轻轻地抿了一口，苦涩与醇厚在舌尖散开，那感觉，就像是味蕾在开一场盛大的派对。\"",
//           "avatarUrl": "https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/audio_store/avator/奇妙惬.jpg",
//           "audioUrl": "{\"neutral\":\"https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/audio_store/奇妙惬-通用.mp3\",\"happy\":\"https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/audio_store/奇妙惬-高兴.mp3\",\"sad\":\"https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/audio_store/奇妙惬-悲伤.mp3\",\"fearful\":\"https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/audio_store/奇妙惬-害怕.mp3\",\"angry\":\"https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/audio_store/奇妙惬-愤怒.mp3\",\"disgusted\":\"https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/audio_store/奇妙惬-厌恶.mp3\",\"surprised\":\"https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/audio_store/奇妙惬-惊讶.mp3\"}",
//           "in_use": "4",
//           "bookmark": "1",
//           "languageSkills": "中文",
//           "membershipGrade": "SVIP",
//           "recommendLevel": null,
//           "halfYear": "38.00",
//           "month": "8.00",
//           "year": "66.00",
//           "createTime": 1748255200000,
//           "updateTime": 1749655217000,
//           "recommendDegree": 102,
//           "isBuy": "0",
//           "apiRecommend": 1,
//           "emotion": "[\"happy\", \"sad\", \"angry\", \"fearful\", \"disgusted\", \"surprised\", \"neutral\"]",
//           "url": "https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/audio_store/奇妙惬-通用.mp3",
//           "duration": 0,
//           "isPlayable": null,
//           "isPlaying": false,
//           "audioElement": null,
//           "isHovered": false,
//           "lastRotation": 0,
//           "volume": 100,
//           "aduio_finished": true
//         }
//       },
//       "intonation": 1,
//       "speech": 1.01,
//       "volume": 97
//     }
//   }



//   "aduio_captions": {
//     "upload_aduio": {
//       "aduio": {
//         "url": "https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/material/202506071130187928/伏夏-蚊香液2.mp3",
//         "try_volume": 47,
//         "volume": 20,
//         "isPlaying": false,
//         "currentTime": 0,
//         "duration": 17.251969,
//         "audioElement": "[object HTMLAudioElement]",
//         "name": "伏夏-蚊香液2.mp3"
//       },
//       "textarea": "听说机智的妈妈已经开始换着号号护牙毛了就是这个迪士尼的蚊香液可旋转插头迪士尼大品牌使用起来无烟无味的孕妇宝宝都能用现在趁着新品活动价还是一杯奶茶的骨折价一气四叶够用一整个夏天了赶紧囤吧",
//       "subtitle_data": []
//     },
//     "choose_music": {
//       "current_classify": "平静",
//       "bgm_url": "https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/material/背景音乐/平静/在432赫兹做梦.mp3",
//       "current_nav": 1,
//       "info": {
//         "id": 228,
//         "theme": "平静",
//         "musicName": "在432赫兹做梦",
//         "ossPath": "https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/material/背景音乐/平静/在432赫兹做梦.mp3",
//         "createTime": [
//           2025,
//           4,
//           7,
//           11,
//           9,
//           8
//         ],
//         "updateTime": [
//           2025,
//           4,
//           7,
//           11,
//           9,
//           8
//         ],
//         "fileExtension": " ",
//         "color": "rgb(174,146,122)",
//         "color1": "rgb(161,189,181)",
//         "color2": "rgb(208,224,136)",
//         "isSelected": false,
//         "isPlaying": false,
//         "time": "06:56",
//         "volume": 80,
//         "url": "https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/material/背景音乐/平静/在432赫兹做梦.mp3"
//       }
//     }
//   }




} )
let drive_tabs = reactive([
	{
		name: '输入文本',
		id: 1
	},
	{
		name: '音频驱动',
		id: 2
	},
])
let current_tab = ref(1)
let change_tabs = (item) => {
	// 获取之前的模式
	const previousMode = current_tab.value;
	const newMode = item.id;

	// 只有在真正切换模式时才重置（避免重复点击同一个标签）
	if (previousMode !== newMode) {
		// 如果要切换到音频驱动模式(id=2)，执行完整重置
		if (newMode === 2) {
			digitalHumanStore.resetForModeSwitch();
		}
		// 如果要切换到输入文本模式(id=1)，执行轻量重置并清除字幕缓存
		else if (newMode === 1) {
			digitalHumanStore.stop();
			digitalHumanStore.clearAudio();
			digitalHumanStore.setCurrentTime(0);

			// 🧹 清除音频驱动模式的字幕缓存，确保预览区域字幕被清除
			digitalHumanStore.clearSubtitleData();

			// 🎭 重置字幕显示状态，确保预览区域字幕被隐藏
			emit('subtitle-toggle', false);
		}
	}

	// 更新当前标签页
	current_tab.value = newMode;
}
let set_data = async (data) => {
	await nextTick()
	let data1=data.digital_human_right_option
	if(data1.type=='text_captions'){
		current_tab.value=1

		let obj={
			captions:data.captions,
			choose_dub:data.choose_dub,
			choose_music:data.choose_music
		}
		text_captions_ref.value.set_data(obj)
	}else{
		current_tab.value=2
		let obj={
			upload_aduio:data1.aduio_data,
			choose_music:data1.choose_music,
			open_captions:data1.open_captions
		}
		await nextTick()
		aduio_captions_ref.value.set_data(obj)

	}
	// text_captions_ref.value.input_text_obj =data.text_captions

// current_tab.value=1
	// text_captions_ref.value.set_data(data.text_captions)


	// aduio_captions_ref.value.input_aduio_obj = data.aduio_captions
}

let digital_human_right_option = (data) => {
	// 🔧 保存数据到lastDigitalHumanRightOption，供getData方法使用
	lastDigitalHumanRightOption.value = { ...data };

	// 🔧 新增：处理输入文本模式的数据传递到父组件
	if (data.type === 'text_captions' && data.audioJson) {
		// 构建传递给父组件的数据
		const audioDataToEmit = {
			openCaptions: data.open_captions,
			isInputTextMode: true,
			// 🔧 关键修复：传递subtitle_json数组
			subtitleData: data.subtitle_json || data.audioJson.subtitle_json || [],
			// 传递音频相关信息
			audioUrl: data.audioJson.wav_url,
			audioLength: data.audioJson.duration,
			audioName: data.audioJson.wav_name
		};

		// 如果有输入文本，也传递
		if (data.audioJson.tts && data.audioJson.tts.text) {
			const inputText = Array.isArray(data.audioJson.tts.text) ? data.audioJson.tts.text[0] : data.audioJson.tts.text;
			if (inputText && inputText.trim() !== '') {
				audioDataToEmit.extractedText = inputText;
			}
		}

		// 通过audio-data-loaded事件传递完整数据
		emit('audio-data-loaded', audioDataToEmit);
	}
	// 🔧 新增：处理音频驱动模式的数据传递到父组件
	else if (data.type === 'aduio_captions' && data.aduio_data) {
		// 构建传递给父组件的数据
		const audioDataToEmit = {
			openCaptions: data.open_captions,
			isInputTextMode: false, // 音频驱动模式
			// 🔧 关键修复：传递subtitle_data_with_time数组
			subtitleData: data.subtitle_data_with_time || [],
			// 传递音频相关信息
			audioUrl: data.aduio_data?.aduio?.url,
			audioLength: data.aduio_data?.aduio?.duration,
			audioName: data.aduio_data?.aduio?.name,
			// 传递提取的文本
			extractedText: data.aduio_data?.textarea
		};

		// 通过audio-data-loaded事件传递完整数据
		emit('audio-data-loaded', audioDataToEmit);
	}

	// 处理实时字幕开关事件（文本模式和音频模式）
	if (data.realtime && (data.type === 'text_captions_toggle' || data.type === 'audio_captions_toggle')) {
		emit('subtitle-toggle', data.open_captions);
		return; // 实时事件直接返回，不执行后续逻辑
	}

	// 🎵 处理音频驱动模式的数据（优先处理，条件放宽）
	if (data.type === 'aduio_captions') {
		// 发射字幕开关状态变更事件
		if (data.open_captions !== undefined) {
			emit('subtitle-toggle', data.open_captions);
		}

		// 从不同来源提取音频URL，设置优先级
		let audioUrl = "";
		let audioLength = 0;
		let subtitleText = "";

		// 优先级1：从 audioJson 获取
		if (data.audioJson) {
			audioUrl = data.audioJson.wav_url || "";
			audioLength = data.audioJson.duration || 0;
			subtitleText = data.audioJson.wav_text || "";
		}

		// 优先级2：从 aduio_data 获取补充信息
		if (data.aduio_data) {
			if (!audioUrl && data.aduio_data.aduio?.url) {
				audioUrl = data.aduio_data.aduio.url;
			}
			if (!subtitleText && data.aduio_data.textarea) {
				subtitleText = data.aduio_data.textarea;
			}
		}

		// 发射音频驱动数据加载事件
		const audioData = {
			// 🎵 音频驱动模式：播放器和接口都使用同一个音频URL
			audioUrl: audioUrl,                             // 播放器音频URL
			originalAudioUrl: audioUrl,                     // 接口传参音频URL（音频驱动模式下与播放器相同）
			subtitleData: data.subtitle_data_with_time || null,  // 音频驱动的详细字幕时间轴数据
			subtitleFile: "",                               // 音频驱动暂时没有字幕文件
			srtContent: "",                                 // 音频驱动暂时没有SRT内容
			audioLength: audioLength > 0 ? audioLength * 1000 : null,  // 只有真实音频时长才设置，否则为null
			openCaptions: data.open_captions,               // 字幕开关状态
			chooseMusic: data.choose_music,                 // 背景音乐数据
			mode: 'audio_drive',                            // 标识为音频驱动模式
			extractedText: subtitleText                     // 提取的文本内容
		};

		console.log('🎵 音频驱动数据传递:', {
			'audioUrl': audioUrl,
			'播放器和接口都使用': audioUrl
		});

		emit('audio-data-loaded', audioData);
		return; // 处理完音频驱动后直接返回
	}

	// 📝 处理输入文本模式的数据
	else if (data.type === 'text_captions') {
		// 发射字幕开关状态变更事件（无论是否有音频数据都要处理）
		if (data.open_captions !== undefined) {
			emit('subtitle-toggle', data.open_captions);
		}

		// 只有在有音频数据时才发射音频加载事件
		if (data.aduio_data) {
			const audioData = {
				// 🎵 播放器使用 audio_file 字段，接口使用 original_audio_url 字段
				audioUrl: data.aduio_data.audio_file || data.aduio_data.original_audio_url,   // 播放器音频URL（优先使用audio_file）
				originalAudioUrl: data.aduio_data.original_audio_url,   // 接口传参音频URL
				subtitleData: data.aduio_data.subtitle_json,    // 字幕JSON数组
				subtitleFile: data.aduio_data.subtitle_file,    // 字幕文件URL
				srtContent: data.aduio_data.srt,                // SRT字幕内容
				audioLength: data.aduio_data.extra_info?.audio_length, // 音频时长（毫秒）
				openCaptions: data.open_captions,               // 字幕开关状态
				chooseMusic: data.choose_music,                 // 背景音乐数据
				mode: 'text_to_speech'                          // 标识为文本转语音模式
			};

			console.log('🎵 音频数据传递:', {
				'audio_file': data.aduio_data.audio_file,
				'original_audio_url': data.aduio_data.original_audio_url,
				'播放器使用': audioData.audioUrl,
				'接口使用': audioData.originalAudioUrl
			});

			emit('audio-data-loaded', audioData);
		}
	}

	// 兼容处理其他情况的字幕开关
	else if (data.open_captions !== undefined) {
		emit('subtitle-toggle', data.open_captions);
	}
};

provide('digital_human_right_option', digital_human_right_option);
let get_data=async()=>{
    await nextTick()
    if(current_tab.value==1){
        text_captions_ref.value.get_data()
        right_operate_ref.value={
            text_captions: text_captions_ref.value.input_text_obj,
        }
    }else{
        aduio_captions_ref.value.get_data()
        right_operate_ref.value={
            aduio_captions: aduio_captions_ref?.value?.input_aduio_obj
        }
    }
	return right_operate_ref.value
}
// ========================================
// 🔗 对外暴露的方法
// ========================================
/**
 * 获取当前右侧操作面板的数据
 * 用于保存数字人作品时收集配置信息
 */
const getData = () => {
	let result = {};

	try {
		// 根据当前标签页获取对应的数据
		if (current_tab.value == 1) {
			// 文本模式
			if (text_captions_ref.value && text_captions_ref.value.get_data) {
				text_captions_ref.value.get_data();
				result = {
					mode: 'text',
					data: text_captions_ref.value.input_text_obj || {}
				};
			}
		} else {
			// 音频模式
			if (aduio_captions_ref.value && aduio_captions_ref.value.get_data) {
				aduio_captions_ref.value.get_data();
				result = {
					mode: 'audio',
					data: aduio_captions_ref.value.input_aduio_obj || {}
				};
			}
		}

		// 🔧 修复：添加digital_human_right_option数据到返回结果中
		if (lastDigitalHumanRightOption.value && Object.keys(lastDigitalHumanRightOption.value).length > 0) {
			result.data = result.data || {};
			result.data.digital_human_right_option = lastDigitalHumanRightOption.value;
		}

		return result;
	} catch (error) {
		return { mode: 'unknown', data: {} };
	}
};
let scheduled = false
let runOnce=()=>{
  if (scheduled) return
  if(digitalHumanStore['originalWorkData']){
		let data=digitalHumanStore['originalWorkData'].commonJson.data
		set_data(data)
	}
  scheduled = true
}
// 组件挂载时尝试执行
onMounted(() => {
  runOnce()
})

// 组件激活时尝试执行（keep-alive 场景）
onActivated(() => {
  runOnce()
})

// 监听数据变化，数据加载完成后执行
watch(
  () => digitalHumanStore['originalWorkData'],
  (newVal) => {
    if (newVal) {
	  scheduled = false
      runOnce()
    }
  },{immediate:true,deep:true}
)


/**
 * 重置右侧面板数据到默认状态
 *
 * 🎯 功能：将右侧操作面板的所有状态重置为默认值，用于新建模式下的数据清空
 *
 * 🧹 重置内容：
 * - 切换到输入文本模式（标签页1）
 * - 重置scheduled标记，允许重新初始化
 * - 清空子组件的内部状态
 *
 * 🔄 触发时机：
 * - 从编辑模式切换到新建模式时
 * - 父组件调用clearMiddleAreaData时
 */
const resetPanelData = async () => {
	try {
		// 重置为输入文本模式（标签页1）
		current_tab.value = 1;

		// 重置scheduled标记，允许重新初始化
		scheduled = false;

		// 等待DOM更新
		await nextTick();

		// 如果输入文本组件存在，尝试重置其状态
		if (text_captions_ref.value && typeof text_captions_ref.value.resetData === 'function') {
			text_captions_ref.value.resetData();
		}

		// 如果音频驱动组件存在，尝试重置其状态
		if (aduio_captions_ref.value && typeof aduio_captions_ref.value.resetData === 'function') {
			aduio_captions_ref.value.resetData();
		}

		console.log('✅ 右侧面板数据已重置为默认状态');
	} catch (error) {
		console.error('❌ 重置右侧面板数据失败:', error);
	}
};

// 暴露方法给父组件
defineExpose({
	getData,
	get_data,
	// 🔧 新增：同步音频驱动模式字幕开关状态的方法
	syncAudioCaptionsState: (isEnabled) => {
		try {
			// 如果当前是音频驱动模式（标签页2），同步字幕开关状态
			if (current_tab.value === 2 && aduio_captions_ref.value) {
				// 调用音频驱动模式组件的同步方法
				if (aduio_captions_ref.value.syncCaptionsState) {
					aduio_captions_ref.value.syncCaptionsState(isEnabled);
				}
			}
		} catch (error) {
			// 同步失败，保持原有状态
		}
	},
	// 🔄 新增：重置面板数据的方法
	resetPanelData
});
</script>
<style lang="scss" scoped>
.right_operate {
	// width: 100%;
	width: 326px;
	box-sizing: border-box;
	padding: 16px 0;
	display: flex;
	flex-direction: column;
	overflow: hidden;

	.right_operate_drive {
		box-sizing: border-box;
		display: flex;
		flex-direction: column;
		flex: 1;
		overflow: hidden;

		.right_operate_drive_tabs {
			padding-left: 18px;
			display: flex;
			align-items: center;
			height: 42px;

			.right_operate_drive_tabs_item {
				line-height: 42px;
				margin-right: 12px;
				padding: 0 8px;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 14px;
				line-height: 42px;
				cursor: pointer;
				color: rgba(0, 0, 0, 0.4);

				&:last-child {
					margin-right: 0;
				}

				&.current {
					color: #000;
				}
			}
		}

		.right_operate_drive_content {
			flex: 1;
			display: flex;
			flex-direction: column;
			overflow: hidden;
		}
	}
}
</style>