# 代码修改前文档检查工作流程

## 工作流程概述

在进行任何代码修改之前，必须首先仔细检查 `docs` 文件夹中的相关 md 文件，以确保了解项目的历史修改记录、功能实现细节和已知问题。本工作流程旨在确保代码修改的连贯性和一致性，避免重复工作或引入已知问题。

## 工作流程步骤

### 1. 确定修改范围
- 明确需要修改的功能模块或组件
- 确定修改的具体内容和预期目标
- 识别可能受影响的其他功能或组件

### 2. 检查相关文档
- 在 `docs` 文件夹中搜索与修改范围相关的 md 文件
- 优先检查以下类型的文档：
  - 功能实现文档（如"XXX功能实现文档.md"）
  - 问题修复记录（如"XXX问题修复记录.md"）
  - 功能优化记录（如"XXX功能优化记录.md"）
  - 组件详解文档（如"XXX组件功能详解.md"）

### 3. 文档内容分析
- 仔细阅读相关文档，了解：
  - 功能的实现原理和设计思路
  - 已知的问题和限制
  - 之前的修改历史和原因
  - 相关的数据结构和接口定义
  - 与其他功能的依赖关系

### 4. 制定修改计划
- 基于文档分析结果，制定详细的修改计划
- 确保修改计划与现有设计保持一致
- 考虑可能的影响和风险
- 制定测试和验证方案

### 5. 实施修改
- 按照修改计划实施代码修改
- 确保修改符合项目的编码规范和最佳实践
- 添加必要的注释和文档

### 6. 创建或更新文档
- 修改完成后，创建或更新相关文档
- 记录修改的内容、原因和影响
- 包含必要的测试结果和验证信息
- 将文档保存到 `docs` 文件夹中

## 文档检查清单

### 修改前检查
- [ ] 是否已确定修改范围和目标？
- [ ] 是否已搜索并阅读相关文档？
- [ ] 是否了解功能的实现原理和设计思路？
- [ ] 是否已知相关问题和限制？
- [ ] 是否了解之前的修改历史？
- [ ] 是否已制定详细的修改计划？
- [ ] 是否已考虑可能的影响和风险？

### 修改后检查
- [ ] 修改是否符合项目的编码规范？
- [ ] 是否已添加必要的注释？
- [ ] 是否已创建或更新相关文档？
- [ ] 文档是否包含修改的内容、原因和影响？
- [ ] 文档是否已保存到 `docs` 文件夹中？

## 文档命名规范

为确保文档的一致性和可查找性，请遵循以下命名规范：

### 功能实现文档
- 格式：`[功能名称]功能实现文档.md`
- 示例：`数字人编辑器功能详解.md`

### 问题修复记录
- 格式：`[问题描述]问题修复记录.md`
- 示例：`字体跳动问题修复记录.md`

### 功能优化记录
- 格式：`[功能名称]功能优化记录.md`
- 示例：`数字人拉伸坐标系精度优化.md`

### 组件详解文档
- 格式：`[组件名称]组件功能详解.md`
- 示例：`PreviewEditor组件功能详解.md`

### 接口文档
- 格式：`[接口名称]接口数据结构文档.md`
- 示例：`getDigitalWork接口数据存储和获取指南.md`

## 注意事项

1. **文档优先**：在进行任何代码修改之前，必须先检查相关文档。
2. **保持更新**：每次修改后，必须更新或创建相关文档。
3. **详细记录**：文档应包含足够的细节，以便其他开发人员理解和维护。
4. **一致性**：确保文档与实际代码实现保持一致。
5. **可查找性**：使用清晰的命名和分类，确保文档易于查找。

## 常见文档类型

以下是项目中常见的文档类型，供参考：

### 项目概述
- `项目概述与功能文档.md`

### 功能实现
- `数字人编辑器功能详解.md`
- `视频编辑功能详解.md`
- `AI配音功能详解.md`

### 问题修复
- `字体跳动问题修复记录.md`
- `坐标系统修复详细文档.md`
- `字幕显示逻辑修复.md`

### 功能优化
- `数字人拉伸坐标系精度优化.md`
- `字幕显示功能优化测试文档.md`
- `商配音色筛选功能优化.md`

### 接口文档
- `getDigitalWork接口数据存储和获取指南.md`
- `接口数据结构修复.md`

### 组件文档
- `PreviewEditor组件位置设置方法未定义错误修复.md`
- `MyWorksDetail组件优化记录.md`

### 工作流程
- `代码修改前文档检查工作流程.md`（本文档）