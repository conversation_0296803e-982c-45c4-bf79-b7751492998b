# 字体大小保存异常修复记录

## 问题描述

**问题现象**：
1. 在数字人编辑器页面（DigitalHumanEditorPage.vue）中，用户在左侧操作面板设置字幕字体大小为18px
2. 点击"生成视频"按钮保存作品时，传递给后端接口的字体大小却变成了38px
3. 这表明在保存过程中，字体大小发生了错误的缩放转换

**后续发现的问题**：
4. 保存的字体大小数据是18px，但在页面回显时，显示的字体大小却是5px
5. 这个差异是由于回显时的反向缩放转换导致的

## 根本原因分析

**问题定位**：
1. **保存问题**：在 `src/views/layout/components/headbar/components/action/index.vue` 文件的 `buildSaveParams` 方法中，字体大小被错误地进行了正向缩放转换。
2. **回显问题**：在 `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue` 文件的回显逻辑中，字体大小被错误地进行了反向缩放转换。

**具体问题**：
1. **保存时的错误逻辑**：
   ```javascript
   // 错误的缩放逻辑
   const scaleY = standardHeight / originalPageSize.height;
   scaledFontSize = Math.round(scaledFontSize * scaleY);
   ```

2. **回显时的错误逻辑**：
   ```javascript
   // 错误的反向缩放逻辑
   const scaleY = standardHeight / previewWindowHeight;
   pageFontSize = Math.round(fontStyleData.fontSize / scaleY);
   ```

**问题分析**：
- 用户设置：18px（页面坐标系）
- 保存时错误缩放：18px × 2.74 ≈ 49px
- 回显时错误缩放：18px ÷ 2.74 ≈ 6.6px → 5px（四舍五入）

## 修复方案

**采用方案**：移除字体大小的缩放处理，直接使用用户设置的原值

**修复理由**：
1. 字体大小是用户直接设置的UI属性，不应该进行坐标系转换
2. 坐标系转换只适用于位置和尺寸，不应该影响字体大小
3. 保持用户设置的字体大小更符合用户期望

## 修复内容

### 1. 修复保存时的字体大小处理

**文件**：`src/views/layout/components/headbar/components/action/index.vue`

**修复位置**：
- subtitleConfigJson.font_size 的缩放处理（第820行左右）
- commonJson.fontStyle.fontSize 的缩放处理（第1020行左右）

**修复内容**：
```javascript
// 修复前（错误）
const scaleY = standardHeight / originalPageSize.height;
scaledFontSize = Math.round(scaledFontSize * scaleY);

// 修复后（正确）
const scaledFontSize = editorData?.subtitleConfig?.fontSize || 18;
```

### 2. 修复回显时的字体大小处理

**文件**：`src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`

**修复位置**：
- commonJson.fontStyle 回显时的字体大小处理（第1787行左右）
- subtitleConfigJson 回显时的字体大小处理（第1860行左右）

**修复内容**：
```javascript
// 修复前（错误）
const scaleY = standardHeight / previewWindowHeight;
pageFontSize = Math.round(fontStyleData.fontSize / scaleY);

// 修复后（正确）
pageFontSize = fontStyleData.fontSize;
```

## 修复效果

**修复前**：
- 用户设置18px → 保存为38px → 回显为5px
- 数据不一致，用户体验差

**修复后**：
- 用户设置18px → 保存为18px → 回显为18px
- 数据完全一致，用户体验良好

## 测试建议

1. **保存测试**：
   - 设置字体大小为18px，点击"生成视频"
   - 检查网络请求中 `subtitleConfigJson.font_size` 是否为18

2. **回显测试**：
   - 加载已保存的作品
   - 检查界面显示的字体大小是否为18px
   - 检查开发者工具中的 `font-size` 样式是否为18px

3. **边界测试**：
   - 测试不同字体大小（12px、24px、36px等）
   - 测试不同宽高比（16:9、9:16）
   - 确保所有情况下都能正确保存和回显

## 技术总结

**核心原则**：
- 字体大小是UI属性，不应受坐标系转换影响
- 保持用户设置的原值，确保数据一致性
- 简化逻辑，提高代码可维护性

**影响范围**：
- 数字人编辑器页面的字幕字体大小功能
- 不影响其他坐标和尺寸的转换逻辑
- 向后兼容，不影响现有功能 