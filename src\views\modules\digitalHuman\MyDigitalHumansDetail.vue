<template>
	<div class="my-digital-humans-detail-app">
		<!-- 顶部导航条区域 -->
		<Headbar />

		<!-- 主要内容区域 -->
		<div class="main-content">
			<div class="page-header">
				<div class="header-content">
					<h1>我的数字人</h1>
					<el-button
						type="default"
						@click="goToDigitalHumanTransition"
						class="back-button"
					>
						返回
					</el-button>
				</div>
			</div>
			
			<div class="content-area">
				<!-- 内容区域，后续添加具体内容 -->
				<div class="placeholder-content">
					<p>我的数字人详情页面</p>
					<p>具体内容将在后续开发中添加</p>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const goToDigitalHumanTransition = () => {
  router.push('/digital-human-transition')
}
// 导入统一的Headbar组件
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'

// 页面挂载时的初始化逻辑
onMounted(() => {
	console.log('我的数字人详情页面已加载')
})
</script>

<style scoped lang="scss">
// 全屏应用容器
.my-digital-humans-detail-app {
	width: 100vw;
	height: 100vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	background-color: #FFFFFF;
}

// 主要内容区域
.main-content {
	flex: 1;
	padding: 20px 20px 20px 78px;
	margin-top: 64px; // 为Headbar组件预留空间
	overflow-y: auto;
	background-color: #FFFFFF;
}

// 页面头部
.page-header {
	margin-bottom: 30px;

	.header-content {
		display: flex;
		align-items: center;
		gap: 40px; // 按钮与标题间距40px
	}

	h1 {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 32px;
		font-weight: 500;
		color: #333;
		margin: 0;
	}
	
	.back-button {
		// 设置按钮样式：白色背景，绿色边框和文字
		&.el-button--default {
			background-color: #FFFFFF;
			border-color: #0AAF60;
			color: #0AAF60;
			
			&:hover, &:focus {
				background-color: #f0f9f0; // 浅绿色背景
				border-color: #0AAF60;
				color: #0AAF60;
			}
		}
	}
}

// 内容区域
.content-area {
	min-height: 400px;
	display: flex;
	align-items: center;
	justify-content: center;
}

// 占位内容样式
.placeholder-content {
	text-align: center;
	color: #666;
	font-size: 16px;
	line-height: 1.6;

	p {
		margin: 10px 0;
	}
}

// 响应式设计
@media (max-width: 768px) {
	.main-content {
		padding: 15px;
		margin-top: 56px;
	}

	.page-header h1 {
		font-size: 24px;
	}
}
</style> 