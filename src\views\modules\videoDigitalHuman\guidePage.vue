<template>
    <div class="guidePage">
        <header class="top-header">
            <Headbar ref="headbarRef" :videoDigitalHumanStep="step" @updateStep="updateStep" />
        </header>
        <div class="content">
            <div class="box" v-if="step == 0">
                <p class="title">只需几步即可创作你的专属数字人</p>
                <div class="videoBox">
                    <video src="@/assets/video/people_basics_introduce.mp4" width="100%" height="100%" controls
                        autoplay></video>
                </div>
                <div class="create" @click="createImmediately">立即创建</div>
                <p class="cue">高质量数字人创建指南</p>
            </div>
            <div class="box" v-if="step == 1">
                <div class="top">
                    <img src="@/assets/images/videoDigitalHuman/back_1.png" @click="back" alt="" />
                    <p class="title">选择创建方式</p>
                </div>
                <div class="message">
                    <div>
                        <span></span>
                        <span>为了获得更逼真的效果，我们建议上传高分辨率的手机或相机拍摄的，约2分钟的视频。</span>
                    </div>
                    <div class="message_2">或者也可以使用电脑摄像头录制快速体验。</div>
                    <div>
                        <span></span>
                        <span>请确保上传的视频是本人或经过本人授权。</span>
                    </div>
                </div>
                <div class="methods">
                    <div class="frame" @click="step = 2">
                        <img src="@/assets/images/videoDigitalHuman/upload.png" alt="" />
                        <div>
                            <span>上传视频</span>
                            <img src="@/assets/images/videoDigitalHuman/arrow.png" />
                        </div>
                    </div>
                    <div class="frame">
                        <img src="@/assets/images/videoDigitalHuman/camera.png" alt="" />
                        <div>
                            <span>摄像头录制</span>
                            <img src="@/assets/images/videoDigitalHuman/arrow.png" />
                        </div>
                    </div>
                </div>
                <div class="subtitle">示例视频</div>
                <div class="videoBox2">
                    <div>
                        <video src="@/assets/video/example1.mp4" width="100%" height="100%" controls></video>
                    </div>
                    <div>
                        <video src="@/assets/video/example2.mp4" width="100%" height="100%" controls></video>
                    </div>
                </div>
            </div>
            <div class="box" v-if="step == 2">
                <div class="top">
                    <img src="@/assets/images/videoDigitalHuman/back_1.png" @click="back" alt="" />
                    <p class="title">本地视频上传</p>
                </div>
                <div class="message">
                    <div>
                        <span></span>
                        <span>选择一个你觉得可以轻松谈论的话题。只要能把你的注意力集中在相机上，并且帮助你进入主持人模式。</span>
                    </div>
                    <div class="message_2" style="margin-bottom: 0;">记得要面带微笑，闭唇停顿。没有话题？</div>
                </div>
                <el-upload class="upload-demo" drag accept=".mp4,.webm" action="#" :auto-upload="false"
                    :before-upload="beforeUpload" :on-change="handleChange">
                    <div class="choose">选择视频</div>
                    <p class="drag">或拖拽文件到这里</p>
                    <p class="tip">横版或竖版视频，支持MP4/WEBM格式，360p-4k分辨率</p>
                    <p class="tip">市场30秒-5分钟，文件大小不超过2G</p>
                </el-upload>
            </div>
            <div class="box" style="height: 675px;" v-if="step == 3">
                <div class="top">
                    <img src="@/assets/images/videoDigitalHuman/back_1.png" @click="back" alt="" />
                    <p class="title">本地视频上传</p>
                </div>
                <div class="message setMessage">
                    <div>要获得最佳、更逼真的头像，请确认您的视频符合以下要求</div>
                    <div>
                        <span></span>
                        <span>您脸部始终清晰可见</span>
                    </div>
                    <div>
                        <span></span>
                        <span>您正视相机</span>
                    </div>
                    <div>
                        <span></span>
                        <span>朗读句子之间有停顿</span>
                    </div>
                    <div>
                        <span></span>
                        <span>环境安静，且光线充足</span>
                    </div>
                </div>
                <div class="videoBox3">
                    <video :src="videoPreviewUrl" width="100%" height="100%" controls></video>
                    <div class="reselect" @click="back">
                        <img src="@/assets/images/videoDigitalHuman/upload1.png" alt="" />
                        <span>重新选择</span>
                    </div>
                </div>
                <div class="setVoice">
                    <span>声音设置</span>
                    <div>
                        <el-switch v-model="setVoice"
                            style="--el-switch-on-color: #0AAF60; --el-switch-off-color: #F1F2F4" />
                        <span>原声</span>
                    </div>
                </div>
                <div class="nextStep" @click="step = 4">下一步</div>
            </div>
            <div class="box setBox" v-if="step == 4">
                <img src="@/assets/img/digital.png" style="width: 236px;height: 133px;" alt="">
                <p>文件上传成功，你的专属数字人已开始制作</p>
                <div>去查看</div>
            </div>
        </div>
    </div>
    <!-- 协议页面 -->
    <agreementPage v-model:visible="dialogAgreementVisible"></agreementPage>
</template>

<script setup>
import { ref } from 'vue'
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue' //header
import agreementPage from './components/agreementPage.vue'  //协议
import { ElMessage } from 'element-plus'
import { useRouter } from 'vue-router'

const step = ref(0)
const dialogAgreementVisible = ref(null)
dialogAgreementVisible.value = localStorage.getItem('userAgreementProtocol') ? false : true
const setVoice = ref(true)
const videoPreviewUrl = ref(null)
const router = useRouter()
// 返回
const back = () => {
    step.value--
}
const updateStep = (val) => {
    if (val.value == 0) {
        router.go(-1)
    } else {
        val.value--
        step.value = val.value
    }

}
// 立即创建
const createImmediately = () => {
    let val = localStorage.getItem('userAgreementProtocol') ? true : false
    if (!val) {
        dialogAgreementVisible.value = true
    } else {
        step.value = 1
    }
}
//上传文件前
const beforeUpload = async (file) => {
    const validTypes = ['video/mp4', 'video/webm'];
    if (!validTypes.includes(file.type)) {
        ElMessage.error('仅支持 MP4/WEBM 格式');
        return false;
    }
    const sizeLimit = 2 * 1024 * 1024 * 1024;
    if (file.size > sizeLimit) {
        ElMessage.error('视频大小不能超过 2GB');
        return false;
    }
    try {
        const { duration } = await getVideoMetadata(file);
        if (duration < 30 || duration > 300) {
            ElMessage.error('视频时长需在30秒到5分钟之间');
            return false;
        }
        return true;
    } catch (error) {
        ElMessage.error('视频解析失败: ' + error.message);
        return false;
    }
}
const getVideoMetadata = (file) => {
    return new Promise((resolve, reject) => {
        const video = document.createElement('video');
        video.preload = 'metadata';
        video.onloadedmetadata = () => {
            URL.revokeObjectURL(video.src);
            resolve({
                duration: video.duration
            });
        };
        video.onerror = () => {
            reject(new Error('无法读取视频信息'));
        };
        video.src = URL.createObjectURL(file);
    });
};
// 文件预览
const handleChange = (file) => {
    if (videoPreviewUrl.value) {
        URL.revokeObjectURL(videoPreviewUrl.value);
    }
    videoPreviewUrl.value = URL.createObjectURL(file.raw);
    step.value = 3
};


</script>

<style scoped lang="scss">
* {
    box-sizing: border-box;
}

.guidePage {
    width: 100%;
    height: 100%;

    .content {
        width: 100%;
        height: calc(100% - 64px);
        background: #F1F2F4;
        margin-top: 64px;
        padding-top: 100px;

        .box {
            width: 755px;
            height: 622px;
            background: #FFFFFF;
            margin: 0 auto;
            padding-top: 10px;

            .title {
                font-size: 22px;
                font-weight: 500;
                color: #000000;
                text-align: center;
                margin-bottom: 30px;
            }

            .videoBox {
                width: 90%;
                margin: 20px auto 10px;
            }

            .create {
                width: 118px;
                height: 36px;
                text-align: center;
                line-height: 36px;
                border-radius: 2px;
                background: #0AAF60;
                font-size: 14px;
                color: #FFFFFF;
                margin: 0 auto;
                cursor: pointer;
            }

            .cue {
                font-size: 14px;
                color: #353D49;
                text-align: center;
                margin-top: 30px;
            }

            .top {
                position: relative;

                img {
                    position: absolute;
                    top: 10px;
                    left: 58px;
                    cursor: pointer;
                }
            }

            .message {
                width: 651px;
                background: rgba(10, 175, 96, 0.1);
                border-radius: 4px;
                padding: 10px 20px;
                margin: 0 auto;

                div {
                    display: flex;
                    align-items: center;

                    span {
                        display: inline-block;
                    }

                    span:first-child {
                        width: 4px;
                        height: 4px;
                        border-radius: 50%;
                        background: #353D49;
                    }

                    span:last-child {
                        margin-left: 12px;
                        font-size: 12px;
                        color: #353D49;
                    }
                }

                .message_2 {
                    margin: 0 0 8px 16px;
                    font-size: 12px;
                    color: #353D49;
                }
            }

            .methods {
                width: 651px;
                margin: 40px auto 0;
                display: flex;
                justify-content: space-between;

                .frame {
                    width: 315px;
                    height: 139px;
                    border: 1px solid rgba(0, 0, 0, 0.1);
                    border-radius: 8px;
                    text-align: center;
                    cursor: pointer;

                    img {
                        margin: 15px 0 0 0;
                    }

                    div {
                        span {
                            font-size: 16px;
                            color: #353D49;
                            font-weight: 500;
                            margin-right: 10px;
                        }
                    }
                }

                .frame:hover {
                    border-color: #0AAF60;
                }
            }

            .subtitle {
                font-size: 14px;
                color: #353D49;
                text-align: center;
                margin: 30px 0 20px 0;
            }

            .videoBox2 {
                display: flex;
                width: 486px;
                justify-content: space-between;
                margin: 0 auto;

                div {
                    width: 228px;
                    height: 127px;
                    border-radius: 7px;
                }
            }

            .choose {
                width: 91px;
                height: 36px;
                border-radius: 2px;
                background: #0AAF60;
                text-align: center;
                line-height: 36px;
                font-size: 14px;
                color: #FFFFFF;
                margin: 90px auto 0;
            }

            .drag {
                font-size: 16px;
                font-weight: 500;
                color: #353D49;
            }

            .tip {
                font-size: 12px;
                color: #88909B;
                margin: 2px 0 0;
            }

            .setMessage {
                display: flex;
                flex-wrap: wrap;

                div:first-child {
                    font-size: 12px;
                    color: #353D49;
                    width: 100%;
                    margin: 5px 0 10px 0;
                }

                div:not(:first-child) {
                    width: 40%;
                    margin-bottom: 8px;
                }
            }

            .videoBox3 {
                width: 651px;
                height: 341px;
                margin: 15px auto 0;
                position: relative;

                .reselect {
                    width: 110px;
                    height: 30px;
                    display: flex;
                    align-items: center;
                    background: rgba(0, 0, 0, 0.4);
                    border-radius: 6px;
                    position: absolute;
                    top: 15px;
                    right: 15px;
                    cursor: pointer;

                    img {
                        margin: 0 5px 0 15px;
                    }

                    span {
                        font-size: 16px;
                        color: #FFFFFF;
                    }
                }
            }

            .setVoice {
                width: 651px;
                display: flex;
                justify-content: space-between;
                margin: 20px auto 0;
                align-items: center;

                span:first-child {
                    font-size: 16px;
                    color: #353D49;
                    font-weight: 500;
                }

                div {
                    display: flex;
                    align-items: center;

                    span {
                        font-size: 13px;
                        color: #303133;
                        margin-left: 10px;
                    }
                }
            }

            .nextStep {
                width: 91px;
                height: 36px;
                background: #0AAF60;
                text-align: center;
                line-height: 36px;
                font-size: 14px;
                color: #FFFFFF;
                margin: 5px auto 0;
                border-radius: 2px;
                cursor: pointer;
            }
        }

        .setBox {
            text-align: center;
            padding-top: 160px;

            p {
                font-size: 24px;
                font-weight: 500;
                color: #000000;
            }

            div {
                width: 132px;
                height: 41px;
                text-align: center;
                line-height: 41px;
                background: #0AAF60;
                border-radius: 6px;
                font-size: 16px;
                color: #fff;
                cursor: pointer;
                margin: 0 auto;
            }
        }
    }
}
</style>
<style lang="scss">
.el-upload-dragger {
    width: 651px;
    height: 398px;
    border: 1px dashed #0AAF60;
    margin: 30px auto 0;
    border-radius: 8px;
}
</style>