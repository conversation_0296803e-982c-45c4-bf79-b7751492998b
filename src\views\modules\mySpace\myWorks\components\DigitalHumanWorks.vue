<template>
    <div class="digitalWorksBox">
        <div class="cue" v-if="!props.hideCue">您的作品将为您保存30天，及时下载保存！</div>
        <div class="worksBox" v-infinite-scroll="props.enableInfiniteScroll ? loadDigitalWorksList : null" style="overflow: auto">
            <div class="project" v-for="(item, index) in digitalList" :key="index">
                <!-- 多选框 -->
                <el-checkbox
                    v-if="props.multiSelectMode && item.status == 1"
                    :model-value="selectedWorkIds.includes(item.id)"
                    @change="handleWorkSelect(item, $event)"
                    class="work-checkbox checkbox-visible"
                />
                
                <!-- 生成中动画 -->
                <div class="mask" v-if="item.status == 0">
                    <img src="@/assets/img/waiting.gif" />
                    <p>生成中，请稍候...</p>
                </div>
                <!-- 失败状态 -->
                <div class="list" v-if="item.status == 2">
                    <div class="audioCover"><img src="@/assets/img/digital.png" style="width: 100%;height: 100%;" />
                    </div>
                    <div class="mask_info">
                        <img src="@/assets/img/delete.png" @click="deleteDigital(item)" />
                        <div class="edit" @click="reEdit(item.id)">重新编辑</div>
                        <div class="failureCue">失败</div>
                    </div>
                </div>
                <!-- 成功状态 -->
                <div class="list" v-if="item.status == 1">
                    <div class="audioCover" v-if="item.screenWidth == '1080'"><img :src="item.previewUrl" alt=""
                            style="width: 100%;height: 100%;" />
                    </div>
                    <div class="audioCover_h" v-if="item.screenWidth == '1920'"><img :src="item.previewUrl" alt=""
                            style="width: 100%;height: 100%;" />
                    </div>
                    <el-dropdown @click.stop @command="command => handleDropdownAction(command, item)">
                        <img src="@/assets/img/三个点.png" class="more-icon" alt="更多" />
                        <template #dropdown>
                            <el-dropdown-menu>
                                <el-dropdown-item command="rename">
                                    <el-icon>
                                        <EditPen />
                                    </el-icon>重命名
                                </el-dropdown-item>
                                <el-dropdown-item command="delete">
                                    <el-icon>
                                        <Delete />
                                    </el-icon>删除
                                </el-dropdown-item>
                                <el-dropdown-item command="move">
                                    <el-icon>
                                        <Position />
                                    </el-icon>移动文件夹
                                </el-dropdown-item>
                            </el-dropdown-menu>
                        </template>
                    </el-dropdown>
                    <p class="time">{{ formatTime(item.duration) }}</p>
                    <img src="@/assets/img/playButton.png" alt="" class="playButtonImg" @click="watchVideo(item)" />
                </div>
                <div class="info">
                    <div>
                        <p class="title">{{ item.title }}</p>
                        <p class="subtitle">{{ item.updatedTime }}</p>
                    </div>
                    <template v-if="!props.showTextButtons">
                        <img style="cursor: pointer;" v-if="item.status == 1" src="@/assets/img/down.png"
                            @click="downLoadVideo(item)" alt="" />
                    </template>
                    <template v-else>
                        <div class="text-buttons" v-if="item.status == 1">
                            <button class="text-btn edit-btn" @click="editFile(item)">编辑</button>
                            <button class="text-btn download-btn" @click="downLoadVideo(item)">下载</button>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
    <!-- 删除确认对话框 -->
    <DeleteConfirmDialog v-model:visible="deleteDialogVisible" :item-title="choosedItem?.title"
        @cancel="handleDeleteCancel" @confirm="handleDeleteConfirm" :show-warning="false" />
    <!-- 重命名对话框 -->
    <RenameDialog v-model:visible="renameDialogVisible" title="修改作品名称" @confirm="handleRenameConfirm" />
    <!-- 移动到项目对话框 -->
    <DigitalMoveToDialog v-model:visible="moveDialogVisible" :selected-item="choosedItem" :project-list="projects"
        @confirm="handleMoveConfirm" />
    <!-- 视频播放对话框 -->
    <DigitalVideoDialog v-model:visible="mediaPlayerDialogVisible" :title="choosedItem?.title"
        :mediaSource="choosedItem?.digitalHumanUrl" :screenWidth="choosedItem?.screenWidth"
        @close="handleMediaDialogClose" @down="downLoadVideo" />
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount, nextTick } from 'vue'
import { Delete, EditPen, Position } from '@element-plus/icons-vue'
import { getDigitalHumanWorksList, updateWorksStatus, deleteDigitalHumanWorks, renameDigitalHumanWorks, getAlbumsByCreator } from '@/api/mySpace.js'
import { useloginStore } from '@/stores/login'
import { useRouter } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import DeleteConfirmDialog from '../../components/DeleteConfirmDialog.vue'
import RenameDialog from '../../components/RenameDialog.vue'
import DigitalMoveToDialog from '../../components/DigitalMoveToDialog.vue'
import DigitalVideoDialog from '../../components/DigitalVideoDialog.vue'

const props = defineProps({ 
    selectedFileId: {}, 
    workTab: {},
    pageSize: {
        type: Number,
        default: 30
    },
    enableInfiniteScroll: {
        type: Boolean,
        default: true
    },
    hideCue: {
        type: Boolean,
        default: false
    },
    showTextButtons: {
        type: Boolean,
        default: false
    },
    multiSelectMode: {
        type: Boolean,
        default: false
    }
})

// 定义事件
const emit = defineEmits(['selection-change'])

const router = useRouter()
const currentPage = ref(1) //数字人作品列表分页
const currentPageSize = ref(props.pageSize)
const totalPage = ref('')
const digitalList = ref(null)
const generatingList = ref(null) //生成中状态列表
const projects = ref(null) //项目列表
const choosedItem = ref({})
const loginStore = useloginStore()
const getUserId = () => {
    return loginStore.userId || ''
}
const deleteDialogVisible = ref(false)
const renameDialogVisible = ref(false)
const moveDialogVisible = ref(false)
const timer = ref(null)
const mediaPlayerDialogVisible = ref(false)

// 多选相关状态
const selectedWorkIds = ref([]) // 使用数组存储选中的作品ID

// 获取数字人作品列表
const getDigitalList = async () => {
    const { records, pages } = await getDigitalHumanWorksList({ userId: getUserId(), pageParam: { pageNum: currentPage.value, pageSize: currentPageSize.value }, albumId: props.selectedFileId ? props.selectedFileId : '' })
    totalPage.value = pages
    if (currentPage.value == 1) {
        digitalList.value = records
    } else {
        digitalList.value = digitalList.value.concat(records)
    }
    generatingList.value = digitalList.value.filter(item => item.status == '0')
    if (generatingList.value.length > 0) {
        // 生成中状态更新
        timer.value = setInterval(() => {
            const ids = generatingList.value.map(item => item.id)
            if (ids.length > 0) {
                updateDigitalList(ids)
            } else {
                clearInterval(timer.value)
                timer.value = null
            }
        }, 30000)
    }
}
// 根据生成中状态id去更新状态
const updateDigitalList = async (idArr) => {
    const data = await updateWorksStatus({ ids: idArr })
    if (data.length > 0) {
        for (let i = 0; i < data.length; i++) {
            if (data[i].status != 0) {
                for (let j = 0; j < digitalList.value.length; j++) {
                    if (data[i].id == digitalList.value[j].id) {
                        digitalList.value[j] = data[i]
                        break
                    }
                }
            }
        }
        generatingList.value = data.filter(item => item.status == 0)
    } else {
        clearInterval(timer.value)
        timer.value = null
    }

}
// 数字人作品滚动加载
const loadDigitalWorksList = () => {
    if (parseFloat(currentPage.value) < parseFloat(totalPage.value)) {
        currentPage.value++
        getDigitalList()
    }
}
// 时间秒数换算
const formatTime = (seconds) => {
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}
// 重新编辑
const reEdit = (id) => {
    // 记录来源页面，用于返回时使用
    const fromPage = router.currentRoute.value.path
    router.push({ 
        path: '/digital-human-editor-page', 
        query: { 
            id: id,
            from: fromPage 
        } 
    });
}

// 编辑文件（从作品项目中编辑）
const editFile = (item) => {
    // 调用现有的reEdit逻辑，传递作品ID
    reEdit(item.id);
}
// 删除数字人作品
const deleteDigital = (item) => {
    choosedItem.value = item
    deleteDialogVisible.value = true
}
const handleDeleteCancel = () => {
    // console.log('删除对话框取消')
}
const handleDeleteConfirm = async () => {
    const data = await deleteDigitalHumanWorks({ id: choosedItem.value.id })
    currentPage.value = 1
    getDigitalList()
    ElMessage.success(data)

}
// 重命名数字人作品
const handleRenameConfirm = async (newName) => {
    const data = await renameDigitalHumanWorks({ id: choosedItem.value.id, title: newName })
    currentPage.value = 1
    getDigitalList()
    ElMessage.success(data)
}
const handleDropdownAction = (command, item) => {
    choosedItem.value = item
    if (command == 'rename') {
        renameDialogVisible.value = true
    } else if (command == 'delete') {
        deleteDialogVisible.value = true
    } else if (command == 'move') {
        moveDialogVisible.value = true
    }
}
// 加载项目文件夹列表
const loadProjectsList = async () => {
    const response = await getAlbumsByCreator({ userId: getUserId() })
    projects.value = response
}
// 确认移动文件夹
const handleMoveConfirm = () => {
    currentPage.value = 1
    getDigitalList()
}
// 下载视频
let loadingInstance = null
const downLoadVideo = (data) => {
    loadingInstance = ElLoading.service({ fullscreen: true, text: '下载中...' })
    if (data.url && data.url != undefined) {
        downloadVideoMethod(data.url, data.filename)
    } else {
        downloadVideoMethod(data.digitalHumanUrl, data.title)
    }
}
const downloadVideoMethod = async (url, filename) => {
    try {
        const response = await fetch(url);
        const blob = await response.blob();
        const blobUrl = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = blobUrl;
        a.download = filename || 'video.mp4';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        // Clean up
        URL.revokeObjectURL(blobUrl);
        nextTick(() => {
            loadingInstance.close()
            ElMessage.success('下载成功')
        })
    } catch (error) {
        console.error('Download failed:', error);
    }
}
// 根据选择文件夹的不同切换列表
watch(() => props.selectedFileId, () => {
    currentPage.value = 1
    getDigitalList()
})
watch(() => props.workTab, () => {
    currentPage.value = 1
    getDigitalList()
}, { deep: true, immediate: true })

watch(() => props.pageSize, (newPageSize) => {
    currentPageSize.value = newPageSize
    currentPage.value = 1
    getDigitalList()
})

// 监听多选模式变化，退出时清空选择
watch(() => props.multiSelectMode, (newVal) => {
    if (!newVal) {
        selectedWorkIds.value.length = 0
    }
})

onMounted(() => {
    loadProjectsList()
})

onBeforeUnmount(() => {
    if (timer.value) {
        clearInterval(timer.value)
        timer.value = null
    }
})
// 查看视频
const watchVideo = (item) => {
    choosedItem.value = item
    mediaPlayerDialogVisible.value = true
}
const handleMediaDialogClose = () => {
    mediaPlayerDialogVisible.value = false
}

// 多选功能相关方法
// 处理单个作品选择
const handleWorkSelect = (item, isSelected) => {
    if (isSelected) {
        if (!selectedWorkIds.value.includes(item.id)) {
            selectedWorkIds.value.push(item.id)
        }
    } else {
        const index = selectedWorkIds.value.indexOf(item.id)
        if (index > -1) {
            selectedWorkIds.value.splice(index, 1)
        }
    }
    emitSelectionChange()
}

// 全选/取消全选
const handleSelectAllWorks = (selectAll) => {
    console.log('全选方法被调用，参数:', selectAll)
    
    if (selectAll) {
        // 全选：只选择成功状态的作品
        const successWorks = digitalList.value.filter(item => item.status == 1)
        console.log('成功状态的作品数量:', successWorks.length)
        
        successWorks.forEach(item => {
            if (!selectedWorkIds.value.includes(item.id)) {
                selectedWorkIds.value.push(item.id)
                console.log('添加作品ID:', item.id)
            }
        })
    } else {
        // 取消全选
        console.log('清空选中状态')
        selectedWorkIds.value.length = 0
    }
    
    console.log('全选后选中作品:', selectedWorkIds.value)
    emitSelectionChange()
}

// 获取总作品数量（只计算成功状态的）
const getTotalWorksCount = () => {
    return digitalList.value ? digitalList.value.filter(item => item.status == 1).length : 0
}

// 刷新作品列表
const refreshWorksList = () => {
    currentPage.value = 1
    selectedWorkIds.value.length = 0
    getDigitalList()
}

// 发送选择变化事件到父组件
const emitSelectionChange = () => {
    const selectedWorks = digitalList.value.filter(item => selectedWorkIds.value.includes(item.id))
    emit('selection-change', selectedWorks)
}

// 暴露给父组件的方法
defineExpose({
    handleSelectAllWorks,
    getTotalWorksCount,
    refreshWorksList
})

</script>

<style lang="scss" scoped>
* {
    box-sizing: border-box;
}

p {
    margin: 0;
}

.digitalWorksBox {
    height: 100%;
    .cue {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        margin: 10px 0 20px 0;
    }

    .worksBox {
        width: 100%;
        display: flex;
        flex-wrap: wrap;
        color: #000000;
        height: calc(100% - 50px);
        scrollbar-width: none; //Firefox
        -ms-overflow-style: none; //IE 和 Edge

        .project {
            margin-right: 30px;

            .list,
            .mask {
                width: 231px;
                height: 160px;
                border-radius: 4px;
                position: relative;
                background: #F1F2F4;
            }

            .mask {
                text-align: center;
                padding-top: 40px;

                p {
                    text-align: center;
                    font-size: 12px;
                    color: rgba(0, 0, 0, 0.65);
                }
            }

            .list {
                display: flex;
                position: relative;

                .audioCover {
                    width: 100px;
                    margin: 0 auto;
                }

                .audioCover_h {
                    width: 100%;
                    height: 100%;
                }

                .time {
                    width: 40px;
                    height: 21px;
                    border-radius: 2px;
                    background: #000000;
                    font-size: 12px;
                    color: #FFFFFF;
                    position: absolute;
                    bottom: 7px;
                    left: 7px;
                    text-align: center;
                    line-height: 21px;
                }

                .playButtonImg {
                    width: 40px;
                    height: 40px;
                    position: absolute;
                    top: 60px;
                    left: 95px;
                    cursor: pointer;
                }

                .mask_info {
                    width: 100%;
                    height: 100%;
                    background: rgba(0, 0, 0, 0.4);
                    position: absolute;
                    top: 0;
                    left: 0;
                    border-radius: 4px;

                    img {
                        position: absolute;
                        top: 8px;
                        right: 8px;
                        cursor: pointer;
                    }

                    .edit {
                        width: 76px;
                        height: 36px;
                        border: 1px solid #FFFFFF;
                        border-radius: 4px;
                        font-size: 14px;
                        color: #FFFFFF;
                        text-align: center;
                        line-height: 36px;
                        margin: 62px auto 0;
                        cursor: pointer;
                    }

                    .failureCue {
                        width: 40px;
                        height: 21px;
                        background: #FF2D55;
                        border-radius: 2px;
                        position: absolute;
                        bottom: 7px;
                        left: 7px;
                        font-size: 12px;
                        color: #FFFFFF;
                        text-align: center;
                        line-height: 21px;
                    }
                }

                .more-icon {
                    width: 14px;
                    height: 14px;
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    cursor: pointer;
                }

                .more-icon:focus {
                    outline: none;
                }
            }

            // 多选框样式
            .work-checkbox {
                position: absolute;
                top: 8px;
                left: 8px;
                z-index: 10;
                border-radius: 50%; // 改为圆形
                padding: 4px; // 增加padding让圆形更明显
                opacity: 1;
                transition: all 0.3s ease;

                :deep(.el-checkbox__input) {
                    .el-checkbox__inner {
                        width: 24px; // 卡片多选框尺寸24x24
                        height: 24px;
                        border-radius: 50%; // 圆形
                        border: 2px solid #0AAF60; // 绿色边框
                        background-color: rgba(10, 175, 96, 0.2); // 浅绿色背景
                        
                        &:hover {
                            border-color: #0AAF60;
                            background-color: rgba(10, 175, 96, 0.3);
                        }

                        // 勾选符号样式
                        &::after {
                            border: 2px solid transparent;
                            border-left: 0;
                            border-top: 0;
                            content: "";
                            height: 10px; // 调整勾选符号大小
                            left: 7px; // 调整位置
                            position: absolute;
                            top: 2px;
                            transform: rotate(45deg) scaleY(0);
                            width: 5px;
                            transition: transform 0.15s ease-in 0.05s;
                            transform-origin: center;
                        }
                    }

                    // 选中状态
                    &.is-checked .el-checkbox__inner {
                        background-color: #0AAF60; // 选中时绿色填充
                        border-color: #0AAF60;

                        // 白色勾选符号
                        &::after {
                            border-color: #fff;
                            transform: rotate(45deg) scaleY(1);
                        }
                    }
                }
            }

                .info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 30px;

        .title {
            font-size: 14px;
            font-weight: 500;
            margin: 15px 0 3px 0;
        }

        .subtitle {
            font-size: 12px;
        }
    }

    .text-buttons {
        display: flex;
        gap: 8px;
        align-items: center;
        margin-top: -8px;
    }

    .text-btn {
        width: 36px;
        height: 17px;
        border: 1px solid #EFEFF1;
        border-radius: 4px;
        background: #EFEFF1;
        cursor: pointer;
        font-size: 12px;
        color: #333;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        line-height: 1;
        padding: 0;

    }

        }

    }

    .worksBox::-webkit-scrollbar {
        display: none;
    }
}
</style>
<style lang="scss">
.el-dropdown-menu__item:not(.is-disabled):hover {
    background-color: #F2F3F5;
    color: #1D2129;
}

.el-loading-spinner .path {
    stroke: #0AAF60;
}

.el-loading-spinner .el-loading-text {
    color: #0AAF60;
}
</style>
