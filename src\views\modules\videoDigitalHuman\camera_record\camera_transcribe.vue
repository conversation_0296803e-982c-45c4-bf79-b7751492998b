<template>
  <div class="camera_transcribe">
    <div class="camera_transcribe_container" :class="camera_transcribe_orientation">
      <!-- 摄像头视频流 -->
      <video ref="videoRef" autoplay muted playsinline></video>
      <!-- 画布用于绘制人脸检测框 -->
      <canvas ref="overlayCanvas"></canvas>

      <!-- 头部轮廓SVG -->
      <img class="camera_transcribe_face-frame" src="@/assets/images/videoDigitalHuman/camera_transcribe_avator.png" alt="">
      <!-- 左上角退出录制按钮 -->
      <button class="camera_transcribe_exit-btn" @click="camera_transcribe_exitRecording">
        退出录制
      </button>
      <!-- 顶部倒计时 -->
      <div class="camera_transcribe_top-info">
        <div class="camera_transcribe_countdown">
          <span class="camera_transcribe_record-dot"></span> {{ camera_transcribe_formattedTime }}
        </div>
        <div class="camera_transcribe_top-info-tip">
          请朗读以下文字完成录制：
        </div>
      </div>

      <!-- 朗读文字容器 -->
      <div class="camera_transcribe_read-text-container" :class="{ scrolling: camera_transcribe_isScrolling }" ref="readTextContainerRef">
        <div class="camera_transcribe_read-text" ref="readTextRef">
          <br />
          您好！我现在感觉很好，语调很轻松，我很有信心能做好这次视频录制。我现在就在镜头前，准备开始。请跟随文字朗读，文字会快速向上滚动，完成后请点击结束录制按钮。
        </div>
      </div>

      <!-- 一开始3秒倒计时动画 -->
      <div class="camera_transcribe_start-countdown" v-if="camera_transcribe_preRecording">
        <div class="camera_transcribe_countdown-text" :key="camera_transcribe_startCountdownText" :class="camera_transcribe_countdownClass">{{ camera_transcribe_startCountdownText }}</div>
      </div>

      <!-- 控制按钮 -->
      <div class="camera_transcribe_controls" v-if="!camera_transcribe_preRecording">
        <button v-if="!camera_transcribe_recording" @click="camera_transcribe_prepareStartRecording">开始录制</button>
        <!-- 倒计时剩余5秒时显示结束录制按钮 -->
        <button v-if="camera_transcribe_recording && camera_transcribe_countdown <= 5" @click="camera_transcribe_stopRecording">结束录制</button>
        <button @click="camera_transcribe_switchOrientation" class="camera_transcribe_switch-btn">切换{{ camera_transcribe_orientation === 'portrait' ? '横屏' : '竖屏' }}</button>
      </div>

      <!-- 录制完成视频回放 -->
      <video
        v-if="camera_transcribe_recordedVideoUrl"
        ref="playbackVideoRef"
        :src="camera_transcribe_recordedVideoUrl"
        @loadedmetadata="onLoadedMetadata"
        @timeupdate="onTimeUpdate"
        controls
        class="camera_transcribe_recorded-video"
      ></video>
       <!-- 进度条 -->
      <input
        v-if="camera_transcribe_recordedVideoUrl"
        type="range"
        min="0"
        :max="videoDuration"
        step="0.01"
        v-model="videoCurrentTime"
        @input="onSeek"
      />
      <!-- 蒙层 -->
      <div class="camera_transcribe_overlay-mask"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, computed,watch, nextTick } from 'vue';
import * as faceapi from 'face-api.js';
// import { createFFmpeg, fetchFile } from '@ffmpeg/ffmpeg';

// let ffmpeg = createFFmpeg({ log: true , corePath: 'https://unpkg.com/@ffmpeg/core@0.11.0/dist/ffmpeg-core.js'});
// let ffmpegLoading = ref(false);
// let ffmpegLoaded = ref(false);
/**
 * 初始化FFmpeg
 */
// let loadFFmpeg = async () => {
//   if (!ffmpegLoaded.value) {
//     ffmpegLoading.value = true;
//     await ffmpeg.load();
//     ffmpegLoaded.value = true;
//     ffmpegLoading.value = false;
//     console.log('FFmpeg加载完成');
//   }
// };
// let processVideoWithFFmpeg = async () => {
//   if (!camera_transcribe_recordedVideoUrl.value) {
//     alert('没有录制视频可处理');
//     return;
//   }
//   await loadFFmpeg();

//   // 获取Blob数据
//   let response = await fetch(camera_transcribe_recordedVideoUrl.value);
//   let data = await response.arrayBuffer();

//   // 写入虚拟文件系统
//   ffmpeg.FS('writeFile', 'input.webm', new Uint8Array(data));

//   // 执行转码命令：webm -> mp4
//   await ffmpeg.run('-i', 'input.webm', '-c:v', 'libx264', '-preset', 'fast', '-movflags', 'faststart', 'output.mp4');

//   // 读取转码后文件
//   let outputData = ffmpeg.FS('readFile', 'output.mp4');

//   // 生成URL供播放或下载
//   let processedBlob = new Blob([outputData.buffer], { type: 'video/mp4' });
//   let processedUrl = URL.createObjectURL(processedBlob);

//   // 这里你可以替换播放视频的URL，或者做其他处理
//   camera_transcribe_recordedVideoUrl.value = processedUrl;

//   console.log('视频处理完成，已转码为mp4');
// };
// DOM引用
let videoRef = ref(null);
let overlayCanvas = ref(null);
let readTextRef = ref(null);
let readTextContainerRef = ref(null);

// 状态变量
let camera_transcribe_recording = ref(false);        // 是否正在录制
let camera_transcribe_preRecording = ref(false);     // 预备录制倒计时状态
let camera_transcribe_countdown = ref(30);           // 录制倒计时秒数
let camera_transcribe_orientation = ref('portrait'); // 竖屏或横屏
let camera_transcribe_recordedVideoUrl = ref('');    // 录制完成视频URL

// 录制相关变量
let mediaRecorder = null;
let recordedChunks = [];
let countdownTimer = null;
let faceDetectionInterval = null;

// 文字滚动状态
let camera_transcribe_isScrolling = ref(false);

// 格式化倒计时显示 mm:ss
let camera_transcribe_formattedTime = computed(() => {
  let min = Math.floor(camera_transcribe_countdown.value / 60).toString().padStart(2, '0');
  let sec = (camera_transcribe_countdown.value % 60).toString().padStart(2, '0');
  return `${min}:${sec}`;
});

// 3秒倒计时动画文本和样式控制
let camera_transcribe_startCountdownText = ref('3');
let camera_transcribe_countdownClass = ref(''); // 用于触发文字放大动画
let startCountdownInterval = null;
let playbackVideoRef = ref(null);
let videoDuration = ref(0);
let videoCurrentTime = ref(0);
/**
 * 预备录制倒计时（3秒）
 */
let camera_transcribe_startPreRecordingCountdown = () => {
  camera_transcribe_preRecording.value = true;
  camera_transcribe_recording.value = false;
  let steps = [
    { text: '3', className: 'scale-up' },
    { text: '2', className: 'scale-up' },
    { text: '1', className: 'scale-up' },
    { text: 'Go!', className: 'scale-up go' },
  ];
  camera_transcribe_playCountdownStep(steps);
};

/**
 * 递归播放倒计时步骤动画
 * @param {Array} steps 倒计时步骤数组
 */
let camera_transcribe_playCountdownStep = (steps) => {
  if (steps.length === 0) {
    camera_transcribe_preRecording.value = false;
    camera_transcribe_startRecording();
    return;
  }
  let current = steps.shift();
  camera_transcribe_startCountdownText.value = current.text;

  camera_transcribe_countdownClass.value = '';
  requestAnimationFrame(() => {
    camera_transcribe_countdownClass.value = current.className;
  });

  setTimeout(() => {
    camera_transcribe_playCountdownStep(steps);
  }, 1500);
};

/**
 * 开始录制倒计时（30秒）
 */
let camera_transcribe_startCountdown = () => {
  camera_transcribe_countdown.value = 30;
  countdownTimer = setInterval(() => {
    if (camera_transcribe_countdown.value > 0) {
      camera_transcribe_countdown.value--;
    } else {
      clearInterval(countdownTimer);
      countdownTimer = null;
      camera_transcribe_isScrolling.value = false; // 文字滚动结束
      camera_transcribe_stopRecording();
    }
  }, 1000);
};

/**
 * 停止录制倒计时
 */
let camera_transcribe_stopCountdown = () => {
  if (countdownTimer) {
    clearInterval(countdownTimer);
    countdownTimer = null;
  }
};

/**
 * 清理人脸检测定时器
 */
let camera_transcribe_clearFaceDetectionInterval = () => {
  if (faceDetectionInterval) {
    clearInterval(faceDetectionInterval);
    faceDetectionInterval = null;
  }
};

/**
 * 加载 face-api.js 模型
 */
let camera_transcribe_loadModels = async () => {
  let MODEL_URL = '/img/module'; // 请确保模型文件放置正确路径
  await faceapi.nets.tinyFaceDetector.loadFromUri(MODEL_URL);
};

/**
 * 启动人脸检测，绘制检测框
 */
let camera_transcribe_detectFaces = async () => {
  if (!videoRef.value || !overlayCanvas.value) return;

  let video = videoRef.value;
  let canvas = overlayCanvas.value;
  let displaySize = { width: video.videoWidth, height: video.videoHeight };

  faceapi.matchDimensions(canvas, displaySize);

  faceDetectionInterval = setInterval(async () => {
    if (video.paused || video.ended) return;

    let detections = await faceapi.detectAllFaces(video, new faceapi.TinyFaceDetectorOptions());

    let resizedDetections = faceapi.resizeResults(detections, displaySize);

    let ctx = canvas.getContext('2d');
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 如果需要绘制检测框，取消下一行注释
    // faceapi.draw.drawDetections(canvas, resizedDetections);
  }, 100);
};

/**
 * 准备开始录制，先执行3秒倒计时
 */
let camera_transcribe_prepareStartRecording = () => {
  camera_transcribe_startPreRecordingCountdown();
};

/**
 * 开始录制视频
 */
let camera_transcribe_startRecording = () => {
  if (!videoRef.value || !videoRef.value.srcObject) {
    alert('摄像头未准备好');
    return;
  }

  // 清理之前录制的视频URL
  if (camera_transcribe_recordedVideoUrl.value) {
    URL.revokeObjectURL(camera_transcribe_recordedVideoUrl.value);
    camera_transcribe_recordedVideoUrl.value = '';
  }

  recordedChunks = [];

  // 录制配置，优先使用 VP8 编码
  let options = { mimeType: 'video/webm;codecs=vp8' };
  if (!MediaRecorder.isTypeSupported(options.mimeType)) {
     console.warn('不支持 video/webm; codecs=vp8，尝试其他格式');
    options = { mimeType: 'video/webm' };
    if (!MediaRecorder.isTypeSupported(options.mimeType)) {
      options = {};
    }
  }

  try {
    mediaRecorder = new MediaRecorder(videoRef.value.srcObject, options);
  } catch (e) {
    alert('当前浏览器不支持MediaRecorder或格式不支持');
    return;
  }

  // 收集录制数据
  mediaRecorder.ondataavailable = (e) => {
    if (e.data.size > 0) recordedChunks.push(e.data);
  };

  // 录制停止时生成视频URL
  mediaRecorder.onstop = async() => {
    let blob = new Blob(recordedChunks, { type: 'video/webm' });
      console.log('Blob大小:', blob.size);
    camera_transcribe_recordedVideoUrl.value = URL.createObjectURL(blob);
    // 调用FFmpeg处理视频（可选）
    // await processVideoWithFFmpeg();
  };

  mediaRecorder.start(10);
  camera_transcribe_recording.value = true;

  camera_transcribe_isScrolling.value = true; // 开始文字滚动
  camera_transcribe_startCountdown();        // 开始30秒倒计时
};

/**
 * 停止录制视频
 */
let camera_transcribe_stopRecording = () => {
  if (mediaRecorder && camera_transcribe_recording.value) {
    mediaRecorder.stop();
  }
  camera_transcribe_recording.value = false;
  camera_transcribe_stopCountdown();
  camera_transcribe_isScrolling.value = false;
};

/**
 * 切换横竖屏
 */
let camera_transcribe_switchOrientation = () => {
  camera_transcribe_orientation.value = camera_transcribe_orientation.value === 'portrait' ? 'landscape' : 'portrait';
};

/**
 * 初始化摄像头视频流
 */
let camera_transcribe_initCamera = async () => {
  try {
    let stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
    if (videoRef.value) {
      videoRef.value.srcObject = stream;

      // 等待视频元数据加载完成，确保videoWidth和videoHeight可用
      await new Promise((resolve) => {
        videoRef.value.onloadedmetadata = () => {
          resolve();
        };
      });

      // 设置canvas尺寸匹配视频尺寸
      if (overlayCanvas.value) {
        overlayCanvas.value.width = videoRef.value.videoWidth;
        overlayCanvas.value.height = videoRef.value.videoHeight;
      }
    }
  } catch (err) {
    alert('无法访问摄像头: ' + err.message);
  }
};

/**
 * 退出录制，释放资源
 */
let camera_transcribe_exitRecording = () => {
  if (camera_transcribe_recording.value) {
    camera_transcribe_stopRecording();
  }
  if (camera_transcribe_preRecording.value) {
    camera_transcribe_preRecording.value = false;
    if (startCountdownInterval) {
      clearInterval(startCountdownInterval);
      startCountdownInterval = null;
    }
  }
  // 停止摄像头流
  if (videoRef.value && videoRef.value.srcObject) {
    let tracks = videoRef.value.srcObject.getTracks();
    tracks.forEach(track => track.stop());
    videoRef.value.srcObject = null;
  }
  camera_transcribe_clearFaceDetectionInterval();
  // 清理录制视频URL
  if (camera_transcribe_recordedVideoUrl.value) {
    URL.revokeObjectURL(camera_transcribe_recordedVideoUrl.value);
    camera_transcribe_recordedVideoUrl.value = '';
  }
  camera_transcribe_recording.value = false;
  camera_transcribe_isScrolling.value = false;
  camera_transcribe_countdown.value = 30;
};
// 监听视频元数据加载完成，获取时长
let onLoadedMetadata = () => {
  setTimeout(() => {
    console.log('视频时长1:', playbackVideoRef.value.duration);
  }, 10000);
   console.log('视频时长:', playbackVideoRef.value.duration);
  videoDuration.value = playbackVideoRef.value.duration;
};

// 监听视频播放进度，更新进度条
let onTimeUpdate = () => {
  videoCurrentTime.value = playbackVideoRef.value.currentTime;
};

// 进度条拖动时，设置视频播放位置
let onSeek = () => {
  if (playbackVideoRef.value) {
    playbackVideoRef.value.currentTime = videoCurrentTime.value;
  }
};

// 监听录制视频URL变化，绑定事件
watch(() => camera_transcribe_recordedVideoUrl.value, async(newUrl) => {
  await nextTick()
  if (playbackVideoRef.value) {
    playbackVideoRef.value.src = newUrl;
    playbackVideoRef.value.load();
    console.log('视频URL:', camera_transcribe_recordedVideoUrl.value);

    playbackVideoRef.value.addEventListener('loadedmetadata', onLoadedMetadata);
    playbackVideoRef.value.addEventListener('timeupdate', onTimeUpdate);
  }
});
// 生命周期钩子
onMounted(async () => {
  console.log(typeof SharedArrayBuffer !== 'undefined' ? '支持' : '不支持');
  await camera_transcribe_loadModels();
  await camera_transcribe_initCamera();
  camera_transcribe_detectFaces();
});

onBeforeUnmount(() => {
  camera_transcribe_exitRecording();
});
</script>

<style scoped lang="scss">
.camera_transcribe {
  width: 1920px;
  height: 889.5px;
  transform-origin: 0 0;
  position: relative;
  min-height: 100%;
  display: flex;
  background-color: rgba(0, 0, 0, 0.5);
  flex-direction: column;
}

.camera_transcribe_container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  user-select: none;

  display: flex;
  justify-content: center;

  &.landscape {
    width: 100vh;
    height: 100vw;
    margin: 0 auto;
  }

  video {
    position: absolute;
    top: 0; left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    z-index: 0;
  }

  canvas {
    position: absolute;
    top: 0; left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
  }

  .camera_transcribe_face-frame {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 400px;
    height: 470px;
    transform: translate(-50%, -50%);
    pointer-events: none;
    z-index: 2;
  }

  /* 左上角退出录制按钮 */
  .camera_transcribe_exit-btn {
    position: absolute;
    top: 12px;
    left: 12px;
    z-index: 10;
    background-color: rgba(255, 0, 0, 0.8);
    border: none;
    padding: 8px 14px;
    border-radius: 20px;
    color: white;
    font-weight: 700;
    cursor: pointer;
    user-select: none;
    transition: background-color 0.3s;

    &:hover {
      background-color: rgba(255, 0, 0, 1);
    }
  }

  /* 朗读文字容器 */
  .camera_transcribe_read-text-container {
    position: absolute;
    left: 50%;
    box-sizing: border-box;
    transform: translateX(-50%);
    z-index: 3;
    user-select: text;
    top: 164px;
    width: 753px;
    height: 168px;
    margin: 0 auto;
    color: #fff;
    border-radius: 16px;
    padding: 24px;
    background: rgba(0, 0, 0, 0.5);
    mask-image: linear-gradient(rgb(0, 0, 0) 0%, rgba(0, 0, 0, 0.5) 22.35%, rgb(0, 0, 0) 50%, rgba(0, 0, 0, 0.5) 77.39%, rgb(0, 0, 0) 100%);
    font-size: 26px;
    font-weight: 400;
    line-height: 1.4;
    overflow: hidden;

    .camera_transcribe_read-text {
      color: white;
      white-space: normal;
      word-break: break-word;
      transform: translateY(75%);
      animation-fill-mode: forwards;
    }

    &.scrolling .camera_transcribe_read-text {
      animation-name: scrollUpFast;
      animation-duration: 30s;
      animation-timing-function: linear;
      animation-iteration-count: 1;
      animation-fill-mode: forwards;
    }
  }

  /* 一开始3秒倒计时动画 */
  .camera_transcribe_start-countdown {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 20;
    transform: translate(-50%, -50%);
    user-select: none;

    .camera_transcribe_countdown-text {
      font-size: 2vw;
      color: white;
      opacity: 0;
      transform-origin: center center;
      animation-fill-mode: forwards;
    }

    .scale-up {
      animation: scaleUpBig 1.5s ease forwards;
      opacity: 1;
    }

    .scale-up.go {
      color: #0AAF60;
    }
  }

  /* 顶部倒计时 */
  .camera_transcribe_top-info {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 4;
    user-select: none;
    text-align: center;

    .camera_transcribe_countdown {
      background: rgba(0, 0, 0, 0.7);
      display: inline-flex;
      align-items: center;
      padding: 6px 14px;
      border-radius: 20px;
      font-weight: 700;
      font-size: 18px;
      color: #fff;

      .camera_transcribe_record-dot {
        width: 12px;
        height: 12px;
        background: red;
        border-radius: 50%;
        margin-right: 10px;
        animation: blink 1s infinite;
      }
    }

    .camera_transcribe_top-info-tip {
      margin-top: 50px;
      color: #fff;
      font-size: 24px;
    }
  }

  /* 控制按钮 */
  .camera_transcribe_controls {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 5;
    display: flex;
    gap: 12px;

    button {
      background-color: #007bff;
      border: none;
      padding: 12px 28px;
      border-radius: 30px;
      color: white;
      font-size: 18px;
      font-weight: 700;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: #0056b3;
      }
    }

    .camera_transcribe_switch-btn {
      background-color: #28a745;

      &:hover {
        background-color: #1e7e34;
      }
    }
  }

  /* 录制完成视频 */
  .camera_transcribe_recorded-video {
    position: absolute;
    top: 0; left: 0;
    width: 100vw;
    height: 100vh;
    object-fit: contain;
    background: black;
    z-index: 10;
  }
}

/* 动画关键帧 */
@keyframes blink {
  0%, 50%, 100% {
    opacity: 1;
  }
  25%, 75% {
    opacity: 0.3;
  }
}

@keyframes scrollUpFast {
  from {
    transform: translateY(75%);
  }
  to {
    transform: translateY(-115%);
  }
}

@keyframes scaleUpBig {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(5);
  }
  100% {
    opacity: 1;
    transform: scale(5);
  }
}
.camera_transcribe_overlay-mask {
  position: absolute;
  top: 0; left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1;
  pointer-events: none; /* 如果需要阻止点击，删除这一行 */
}
</style>