<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浪浪山小妖怪海报</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @keyframes cloud-drift {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        @keyframes leaf-fall {
            0% { transform: translateY(-100%) rotate(0deg); }
            100% { transform: translateY(100vh) rotate(360deg); }
        }
        .chinese-ink-text {
            text-shadow: 3px 3px 0 rgba(0,0,0,0.1);
            font-family: '<PERSON>', cursive;
        }
    </style>
</head>
<body class="bg-gradient-to-b from-[#c3e4d9] to-[#a8d5c9] min-h-screen relative overflow-hidden">
    <!-- 山体背景 -->
    <div class="absolute bottom-0 w-full h-2/5 bg-gradient-to-t from-[#5a877a] to-[#3b6659]"></div>
    
    <!-- 主标题 -->
    <h1 class="chinese-ink-text text-6xl text-center pt-12 text-[#2d5047] relative z-10">
        浪浪山奇谭
        <span class="block text-3xl mt-2 text-[#e67e22]">—— 小妖修行日记</span>
    </h1>

    <!-- 小妖怪角色 -->
    <div class="absolute left-1/4 top-1/3 w-40 h-40 animate-bounce">
        <div class="w-full h-full bg-[#f9c794] rounded-full relative">
            <div class="absolute top-1/4 left-1/4 w-8 h-8 bg-white rounded-full"></div>
            <div class="absolute top-1/4 right-1/4 w-8 h-8 bg-white rounded-full"></div>
            <div class="absolute bottom-4 inset-x-4 h-8 bg-[#e67e22] rounded-full"></div>
        </div>
    </div>

    <!-- 动态云朵 -->
    <div class="absolute top-20 w-24 h-12 bg-white/30 rounded-full animate-cloud-drift" 
         style="animation-duration: 20s; left: 10%"></div>
    <div class="absolute top-32 w-32 h-16 bg-white/40 rounded-full animate-cloud-drift"
         style="animation-duration: 25s; left: 30%"></div>

    <!-- 落叶动画 -->
    <div class="leaf absolute top-0 left-1/4 w-4 h-4 bg-[#e67e22] rounded-full"
         style="animation: leaf-fall 8s linear infinite"></div>

    <!-- 水墨山石 -->
    <div class="absolute bottom-0 right-20 w-48 h-48 bg-gradient-to-r from-[#3b6659] to-[#2d5047] rounded-l-full transform -skew-x-12"></div>

    <!-- 宣传文字 -->
    <div class="absolute bottom-20 left-10 text-[#2d5047] space-y-2">
        <p class="text-xl font-bold">🐾 萌妖修行路</p>
        <p class="text-lg">🍃 山间奇遇记</p>
        <p class="text-lg">🎋 水墨仙侠风</p>
    </div>
</body>
</html>