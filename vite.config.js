import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path';
import Inspector from 'vite-plugin-vue-inspector'
import { VueMcp } from 'vite-plugin-vue-mcp'
import os from 'os';
import fs from 'fs';
// import { webUpdateNotice } from '@plugin-web-update-notification/vite'

// 智能检测VSCode路径的函数（增强版）
function getVSCodePath() {
    const username = os.userInfo().username;
    console.log(`🔍 当前用户: ${username}`);

    const possiblePaths = [
        // 用户级安装路径（最常见）
        `C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin\\code.exe`,
        `C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe`,

        // 系统级安装路径
        'C:\\Program Files\\Microsoft VS Code\\bin\\code.exe',
        'C:\\Program Files\\Microsoft VS Code\\Code.exe',
        'C:\\Program Files (x86)\\Microsoft VS Code\\bin\\code.exe',
        'C:\\Program Files (x86)\\Microsoft VS Code\\Code.exe',

        // 可能的自定义安装路径
        'D:\\VScode\\Microsoft VS Code\\bin\\code.exe',
        'D:\\VScode\\Microsoft VS Code\\Code.exe',
        'D:\\Microsoft VS Code\\bin\\code.exe',
        'D:\\Microsoft VS Code\\Code.exe',
        'C:\\VSCode\\bin\\code.exe',
        'C:\\VSCode\\Code.exe',
    ];

    console.log('🔍 检查 VSCode 安装路径...');

    // 检查哪个路径存在
    for (let i = 0; i < possiblePaths.length; i++) {
        const vscodePath = possiblePaths[i];
        try {
            if (fs.existsSync(vscodePath)) {
                console.log(`✅ 找到VSCode: ${vscodePath}`);
                return vscodePath;
            } else {
                console.log(`❌ 不存在: ${vscodePath}`);
            }
        } catch (error) {
            console.log(`⚠️ 检查失败: ${vscodePath} - ${error.message}`);
        }
    }

    // 如果都没找到，返回最常见的路径并给出详细提示
    const defaultPath = possiblePaths[0];
    console.warn('⚠️ 未找到VSCode安装路径，使用默认路径');
    console.warn('💡 请检查以下位置是否有VSCode安装:');
    console.warn('   - C:\\Program Files\\Microsoft VS Code\\');
    console.warn(`   - C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\`);
    console.warn('   - 或手动指定正确路径');

    return defaultPath;
}

// TODO: 请安装stagewise工具栏依赖:
// yarn add --dev @stagewise/toolbar-vue --ignore-engines
// 如果遇到权限问题，尝试以管理员身份运行命令提示符或关闭可能占用esbuild.exe的进程

// 自定义版本生成插件
function versionPlugin() {
    return {
        name: 'version-plugin',
        apply: 'build', // 仅在构建时应用
        generateBundle() {
            // 生成版本信息（使用时间戳作为版本号）
            const version = Date.now().toString();
            const content = JSON.stringify({ version });
            
            // 输出version.json文件
            this.emitFile({
                type: 'asset',
                fileName: 'version.json',
                source: content
            });
            
            console.log(`Generated version.json with version: ${version}`);
        }
    };
}

// https://vite.dev/config/
export default defineConfig(({ mode }) => ({
    plugins: [
        vue(),
        
        Inspector({
            enabled: false,
            toggleButtonVisibility: 'always',
            toggleButtonPos: 'bottom-left',
            // 使用检测到的正确VSCode路径，避免与Cursor冲突
            launchEditor: 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\Code.exe',
            // 备用方案（如果上面不工作）：
            // launchEditor: getVSCodePath(), // 自动检测路径
            // launchEditor: 'cursor', // 如果要切换回Cursor
            // 禁用在编辑器打开时自动关闭inspector
            disableInspectorOnEditorOpen: false,
            // 添加调试信息
            appendTo: false
        }),
        VueMcp(),
        // webUpdateNotice({
        //     logVersion: true,
        //     locale: 'zh_CN',
        //     notificationProps: {
        //         title: '系统更新',
        //         description: '发现新版本，请刷新页面获取最新内容',
        //         buttonText: '刷新',
        //         dismissButtonText: '稍后'
        //     }
        // })
        versionPlugin(), // 使用自定义版本插件


    ],
    base: '/',  // 关键配置
    server: {
        host: '0.0.0.0', // 允许局域网访问
        port: 5173, // 自定义端口
        open: true, // 启动后自动打开浏览器
        proxy: { // 代理配置（解决跨域）
            // 代理规则 2：匹配 /material/api 子路径（更精确）

            '/material': {
                target: 'http://************:8083', // 开发环境下使用HTTP
                changeOrigin: true,
                rewrite: (path) => path.replace(/^\/material/, '/material') // 关键修改：保留 /material 前缀
            },
           
        },
        hmr: {
            overlay: false // 禁用热更新错误覆盖层，避免错误弹窗干扰开发
        },
        fs: {
            allow: ['.'] // 允许访问项目根目录及子目录
        },
        
    },
    build: {
        outDir: 'dist', // 修改为固定目录，不再按环境生成
        emptyOutDir: true,       // 构建前清空目录
        sourcemap: false, // 完全禁用Source Map
        rollupOptions: {
            // 排除开发调试文件和文档文件
            external: (id) => {
                return id.includes('/temp/') ||
                       id.includes('\\temp\\') ||
                       id.includes('/docs/') ||
                       id.includes('\\docs\\')
            },
            // 输入时过滤文件
            input: {
                main: path.resolve(__dirname, 'index.html')
            }
        },
        // 确保assets处理时排除指定文件
        assetsInclude: (file) => {
            // 排除临时文件和文档文件
            return !file.includes('/temp/') &&
                   !file.includes('\\temp\\') &&
                   !file.includes('/docs/') &&
                   !file.includes('\\docs\\')
        }
    },
    define: {
        ENV_BASE: JSON.stringify(process.env.VITE_API_BASE_URL)
    },
    resolve: {
        alias: {
            '@': path.resolve(__dirname, 'src'), // 路径别名
            '#': path.resolve(__dirname, 'types')
        }
    },
    optimizeDeps: {
    include: ['@ffmpeg/ffmpeg']
  }
}))
