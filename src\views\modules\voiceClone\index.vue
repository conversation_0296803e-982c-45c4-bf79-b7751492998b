<template>
    <div class="voice-clone-container">
        <!-- 快速复刻组件 -->
        <QuickClone v-if="currentView === 'quick-clone'" @back="handleBackToMain" @goToHome="handleGoToHome"
            @upload="handleUpload" @record="handleRecord" />

        <!-- 主页面内容 -->
        <div v-else>
            <!-- 快速入口标题 -->
            <div class="quick-entry-title">
                <h2>快捷入口<span>*说明：音色克隆功能仅配音会员可购买，且克隆后的音色使用时需要消耗字符，请确保已开通VIP或者SVIP会员。</span></h2>
            </div>

            <!-- 三个功能卡片区域 -->
            <div class="clone-options-section">
                <div class="clone-options-grid">
                    <!-- 快速复刻卡片 -->
                    <div class="clone-card quick-clone" :class="{ active: selectedOption === 'quick' }">
                        <img :src="piaochuang" alt="首克送一个月VIP" class="quick-clone-badge" />
                        <div class="card-content">
                            <div class="card-header">
                                <div class="card-title-section">
                                    <h3 class="card-title">快速克隆</h3>
                                    <p class="card-subtitle">仅需录制5-30秒内语料</p>
                                    <div class="card-detail-container">
                                        <p class="card-detail">
                                            开放环境中录制秒级别录音即可极速拥有专属定制音色，<br>
                                            可满足基本的配音需求，支持32个小语种。
                                        </p>
                                        <div class="card-detail-btn-wrapper">
                                            <button class="card-detail-btn" @click="handleQuickClone">立即体验</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 白色容器组 -->
                            <div class="card-white-containers">
                                <div class="card-white-container">
                                    <!-- 用户信息显示区域 -->
                                    <div class="user-info-section">
                                        <div class="user-avatar wangye-avatar"></div>
                                        <span class="user-name">王也</span>
                                    </div>
                                    <!-- 用户描述文字 -->
                                    <div class="user-description">该案例适用于短视频创作及各类影视二创。</div>
                                    <!-- 播放按钮组 -->
                                    <div class="play-buttons">
                                        <div class="play-button-item">
                                            <img :src="isQuick1Playing1 ? kelongplay3 : kelongplay1" alt="播放1" class="play-img"
                                                @click="playQuickAudio(1, 1)" />
                                            <span class="play-label">本人声音</span>
                                        </div>
                                        <div class="play-button-item">
                                            <img :src="isQuick1Playing2 ? kelongplay4 : kelongplay2" alt="播放2" class="play-img"
                                                @click="playQuickAudio(1, 2)" />
                                            <span class="play-label">克隆声音</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-white-container">
                                    <!-- 用户信息显示区域 -->
                                    <div class="user-info-section">
                                        <div class="user-avatar guzhixia-avatar"></div>
                                        <span class="user-name">顾知夏</span>
                                    </div>
                                    <!-- 用户描述文字 -->
                                    <div class="user-description">该案例适用于第一人称视角的独白或走心旁白。</div>
                                    <!-- 播放按钮组 -->
                                    <div class="play-buttons">
                                        <div class="play-button-item">
                                            <img :src="isQuick2Playing1 ? kelongplay3 : kelongplay1" alt="播放1" class="play-img"
                                                @click="playQuickAudio(2, 1)" />
                                            <span class="play-label">本人声音</span>
                                        </div>
                                        <div class="play-button-item">
                                            <img :src="isQuick2Playing2 ? kelongplay4 : kelongplay2" alt="播放2" class="play-img"
                                                @click="playQuickAudio(2, 2)" />
                                            <span class="play-label">克隆声音</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 精品克隆卡片 -->
                    <div class="clone-card premium-clone" :class="{ active: selectedOption === 'premium' }">
                        <div class="card-content">
                            <div class="card-header">
                                <div class="card-title-section">
                                    <h3 class="card-title">精品克隆</h3>
                                    <p class="card-subtitle">棚录30分钟以上语料，精细语音复制</p>
                                    <div class="card-detail-container">
                                        <p class="card-detail">
                                            高品质声音复刻，95%音色还原，情感丰富，适用于各类<br>
                                            场景的有声创作及日常对话，支持32个小语种。
                                        </p>
                                        <div class="card-detail-btn-wrapper">
                                            <button class="card-detail-btn" @click="handlePremiumClone">立即咨询</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 白色容器组 -->
                            <div class="card-white-containers">
                                <div class="card-white-container">
                                    <!-- 用户信息显示区域 -->
                                    <div class="user-info-section">
                                        <div class="user-avatar suhe-avatar"></div>
                                        <span class="user-name">苏禾</span>
                                    </div>
                                    <!-- 用户描述文字 -->
                                    <div class="user-description">此案例适用于日常对话，广告、走心旁白等。</div>
                                    <!-- 播放按钮组 -->
                                    <div class="play-buttons">
                                        <div class="play-button-item">
                                            <img :src="isPremium1Playing1 ? jingpin3 : jingpin1" alt="播放1" class="play-img"
                                                @click="playPremiumAudio(1, 1)" />
                                            <span class="play-label">本人声音</span>
                                        </div>
                                        <div class="play-button-item">
                                            <img :src="isPremium1Playing2 ? jingpin4 : jingpin2" alt="播放2" class="play-img"
                                                @click="playPremiumAudio(1, 2)" />
                                            <span class="play-label">克隆声音</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-white-container">
                                    <!-- 用户信息显示区域 -->
                                    <div class="user-info-section">
                                        <div class="user-avatar mingyuan-avatar"></div>
                                        <span class="user-name">明远</span>
                                    </div>
                                    <!-- 用户描述文字 -->
                                    <div class="user-description">此案例适用于短视频创作、书籍介绍、国学介绍等。</div>
                                    <!-- 播放按钮组 -->
                                    <div class="play-buttons">
                                        <div class="play-button-item">
                                            <img :src="isPremium2Playing1 ? jingpin3 : jingpin1" alt="播放1" class="play-img"
                                                @click="playPremiumAudio(2, 1)" />
                                            <span class="play-label">本人声音</span>
                                        </div>
                                        <div class="play-button-item">
                                            <img :src="isPremium2Playing2 ? jingpin4 : jingpin2" alt="播放2" class="play-img"
                                                @click="playPremiumAudio(2, 2)" />
                                            <span class="play-label">克隆声音</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- SFT克隆卡片 -->
                    <div class="clone-card sft-clone" :class="{ active: selectedOption === 'sft' }">
                        <div class="card-content">
                            <div class="card-header">
                                <div class="card-title-section">
                                    <h3 class="card-title">SFT克隆</h3>
                                    <p class="card-subtitle">棚录2-3小时语料，超精细监督微调语音复制</p>
                                    <div class="card-detail-container">
                                        <p class="card-detail">
                                            99%超细节还原真实音色，情感细腻，可完美进行各类<br>
                                            场景的声音播报，支持32个小语种。
                                        </p>
                                        <div class="card-detail-btn-wrapper">
                                            <button class="card-detail-btn" @click="handleSftClone">立即咨询</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 白色容器组 -->
                            <div class="card-white-containers">
                                <div class="card-white-container">
                                    <!-- 用户信息显示区域 -->
                                    <div class="user-info-section">
                                        <div class="user-avatar lixiuyuan-avatar"></div>
                                        <span class="user-name">李修远</span>
                                    </div>
                                    <!-- 用户描述文字 -->
                                    <div class="user-description">此案例适用于汇报片、宣传片等风格的商业配音录制。</div>
                                    <!-- 播放按钮组 -->
                                    <div class="play-buttons">
                                        <div class="play-button-item">
                                            <img :src="isSft1Playing1 ? kelong3 : kelong1" alt="播放1" class="play-img"
                                                @click="playSftAudio(1, 1)" />
                                            <span class="play-label">本人声音</span>
                                        </div>
                                        <div class="play-button-item">
                                            <img :src="isSft1Playing2 ? kelong4 : kelong2" alt="播放2" class="play-img"
                                                @click="playSftAudio(1, 2)" />
                                            <span class="play-label">克隆声音</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-white-container">
                                    <!-- 用户信息显示区域 -->
                                    <div class="user-info-section">
                                        <div class="user-avatar shenzhiyao-avatar"></div>
                                        <span class="user-name">沈知遥</span>
                                    </div>
                                    <!-- 用户描述文字 -->
                                    <div class="user-description">此案例适用于纪录片，宣传片等风格的商业配音录制。</div>
                                    <!-- 播放按钮组 -->
                                    <div class="play-buttons">
                                        <div class="play-button-item">
                                            <img :src="isSft2Playing1 ? kelong3 : kelong1" alt="播放1" class="play-img"
                                                @click="playSftAudio(2, 1)" />
                                            <span class="play-label">本人声音</span>
                                        </div>
                                        <div class="play-button-item">
                                            <img :src="isSft2Playing2 ? kelong4 : kelong2" alt="播放2" class="play-img"
                                                @click="playSftAudio(2, 2)" />
                                            <span class="play-label">克隆声音</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的克隆声音部分 -->
            <div class="voice-list-section">
                <div class="section-header">
                    <h3 class="section-title">我的克隆声音</h3>
                    <span class="section-subtitle">（仅展示已授权成功通过的内容克隆音色）</span>
                </div>

                <!-- 声音列表表格 -->
                <div class="voice-table-container">
                    <el-table :data="voiceList" class="voice-table" :height="370" v-loading="tableLoading">
                        <el-table-column prop="platformNickname" label="声音名称" min-width="200">
                            <template #default="{ row }">
                                <div class="voice-name-cell">
                                    <div v-if="row.avatarUrl" class="voice-avatar">
                                        <img :src="row.avatarUrl" :alt="row.platformNickname" />
                                    </div>
                                    <div v-else class="voice-avatar default-avatar"></div>
                                    <span class="voice-name">{{ row.platformNickname }}</span>
                                </div>
                            </template>
                        </el-table-column>

                        <el-table-column prop="createTime" label="时间" min-width="250">
                            <template #default="{ row }">
                                <span class="voice-time">{{ row.createTime }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column prop="cloneType" label="克隆方式" min-width="300">
                            <template #default="{ row }">
                                <span class="clone-method">{{ getCloneTypeName(row.cloneType) }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column label="操作" width="150" header-align="center" align="center">
                            <template #default="{ row }">
                                <el-dropdown
                                    trigger="click"
                                    placement="bottom-start"
                                    @command="(command) => handleDropdownCommand(command, row)">
                                    <span class="use-voice-link">使用音色</span>
                                    <template #dropdown>
                                        <el-dropdown-menu>
                                            <el-dropdown-item command="ai-dubbing">制作AI配音</el-dropdown-item>
                                            <el-dropdown-item command="digital-human">制作数字人视频</el-dropdown-item>
                                        </el-dropdown-menu>
                                    </template>
                                </el-dropdown>
                            </template>
                        </el-table-column>
                    </el-table>

                    <!-- 添加分页组件 -->
                    <div class="pagination-container">
                        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
                            :page-sizes="[5, 10, 20, 50]" :total="total"
                            layout="total, sizes, prev, pager, next, jumper" @size-change="handleSizeChange"
                            @current-change="handleCurrentChange" />
                    </div>
                </div>
            </div>
        </div>

        <!-- 联系客服弹窗组件 -->
        <Contact ref="contactRef" />


    </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useloginStore } from '@/stores/login'
import { useSoundStore } from '@/stores/modules/soundStore.js'
import { getCloneList } from '@/api/voiceClone'
import QuickClone from './components/QuickClone.vue'
// 导入联系客服弹窗组件
import Contact from '../realVoice/components/index/contact.vue'
// 导入用户权益数据刷新 hook
import { useUserBenefits } from '@/views/modules/AIDubbing/hook/useUserInfo.js'

// 导入播放按钮图片
import kelongplay1 from '@/assets/img/kelongplay1.png'
import kelongplay2 from '@/assets/img/kelongplay2.png'
import kelongplay3 from '@/assets/img/kelongplay3.png'
import kelongplay4 from '@/assets/img/kelongplay4.png'
import piaochuang from '@/assets/img/piaochuang.png'
// 导入精品克隆播放按钮图片
import jingpin1 from '@/assets/img/jinmgpin1.png'
import jingpin2 from '@/assets/img/jinmgpin2.png'
import jingpin3 from '@/assets/img/jinmgpin3.png'
import jingpin4 from '@/assets/img/jinmgpin4.png'
// 导入SFT克隆播放按钮图片
import kelong1 from '@/assets/img/kelong1.png'
import kelong2 from '@/assets/img/kelong2.png'
import kelong3 from '@/assets/img/kelong3.png'
import kelong4 from '@/assets/img/kelong4.png'


// 获取路由实例
const router = useRouter()
const route = useRoute()

// 获取用户信息
const userStore = useloginStore()
const userId = computed(() => userStore.userId)

// 获取soundStore实例
const soundStore = useSoundStore()

// 用户权益数据刷新功能
const { fetchUserBenefits } = useUserBenefits()

// 响应式状态管理
const currentView = ref('main') // 当前视图：'main' | 'quick-clone' | 'premium-clone' | 'sft-clone'
const selectedOption = ref('') // 当前选中的克隆选项
// 联系客服弹窗引用
const contactRef = ref(null)



// 快速克隆播放状态管理 - 为每个容器的每个按钮创建独立状态
const isQuick1Playing1 = ref(false) // 快速克隆第一个容器第一个播放按钮
const isQuick1Playing2 = ref(false) // 快速克隆第一个容器第二个播放按钮
const isQuick2Playing1 = ref(false) // 快速克隆第二个容器第一个播放按钮
const isQuick2Playing2 = ref(false) // 快速克隆第二个容器第二个播放按钮

// 精品克隆播放状态管理 - 为每个容器的每个按钮创建独立状态
const isPremium1Playing1 = ref(false) // 精品克隆第一个容器第一个播放按钮
const isPremium1Playing2 = ref(false) // 精品克隆第一个容器第二个播放按钮
const isPremium2Playing1 = ref(false) // 精品克隆第二个容器第一个播放按钮
const isPremium2Playing2 = ref(false) // 精品克隆第二个容器第二个播放按钮

// SFT克隆播放状态管理 - 为每个容器的每个按钮创建独立状态
const isSft1Playing1 = ref(false) // SFT克隆第一个容器第一个播放按钮
const isSft1Playing2 = ref(false) // SFT克隆第一个容器第二个播放按钮
const isSft2Playing1 = ref(false) // SFT克隆第二个容器第一个播放按钮
const isSft2Playing2 = ref(false) // SFT克隆第二个容器第二个播放按钮

// 音频元素管理 - 为每个播放按钮创建独立的音频元素
const audioElements = ref({
    quick1_1: null, // 快速克隆第一个容器第一个按钮
    quick1_2: null, // 快速克隆第一个容器第二个按钮
    quick2_1: null, // 快速克隆第二个容器第一个按钮
    quick2_2: null, // 快速克隆第二个容器第二个按钮
    premium1_1: null, // 精品克隆第一个容器第一个按钮
    premium1_2: null, // 精品克隆第一个容器第二个按钮
    premium2_1: null, // 精品克隆第二个容器第一个按钮
    premium2_2: null, // 精品克隆第二个容器第二个按钮
    sft1_1: null, // SFT克隆第一个容器第一个按钮
    sft1_2: null, // SFT克隆第一个容器第二个按钮
    sft2_1: null, // SFT克隆第二个容器第一个按钮
    sft2_2: null  // SFT克隆第二个容器第二个按钮
})

// 声音列表数据
const voiceList = ref([])
// 表格加载状态
const tableLoading = ref(false)
// 总记录数
const total = ref(0)

// 添加分页相关变量和方法
const currentPage = ref(1)
const pageSize = ref(10)

// 获取声音克隆列表数据
const fetchVoiceList = async () => {
    if (!userId.value) {
        console.warn('用户ID不存在，无法获取声音克隆列表')
        return
    }

    tableLoading.value = true
    try {
        const params = {
            userCloneVoice: {
                userId: userId.value
            },
            pageParam: {
                pageNum: currentPage.value,
                pageSize: pageSize.value
            }
        }

        const res = await getCloneList(params)
        console.log('获取声音克隆列表数据', res)
        // 直接使用返回数据
        voiceList.value = res.records || []
        total.value = res.total || 0
    } catch (error) {
        console.error('获取声音克隆列表异常：', error)
    } finally {
        tableLoading.value = false
    }
}

// 处理分页大小变化
const handleSizeChange = (size) => {
    pageSize.value = size
    currentPage.value = 1 // 切换每页条数时重置为第一页
    fetchVoiceList()
}

// 处理当前页变化
const handleCurrentChange = (page) => {
    currentPage.value = page
    fetchVoiceList()
}



// 初始化支付成功弹窗状态
const initializePaymentSuccessDialogStatus = async () => {
    try {
        console.log('=== 初始化支付成功弹窗状态开始 ===')
        
        // 刷新用户权益数据，获取最新的 frist_buy_clone 状态
        console.log('🔄 刷新用户权益数据以获取最新状态...')
        await fetchUserBenefits()
        console.log('✅ 用户权益数据刷新完成')
        
        // 获取最新的会员信息
        const memberInfo = userStore.memberInfo
        console.log('当前会员信息:', memberInfo)
        
        if (memberInfo) {
            const fristBuyClone = memberInfo.frist_buy_clone
            console.log('当前 frist_buy_clone 值:', fristBuyClone)
            console.log('值类型:', typeof fristBuyClone)
            console.log('是否为 null:', fristBuyClone === null)
            console.log('是否为 undefined:', fristBuyClone === undefined)
            console.log('是否为空字符串:', fristBuyClone === '')
            
            // 根据 frist_buy_clone 的值设置 hasSeenVoiceClonePaymentSuccess
            if (fristBuyClone === null || fristBuyClone === undefined || fristBuyClone === '') {
                // 用户从未购买过克隆服务，设置为 false（没有看过弹窗）
                localStorage.setItem('hasSeenVoiceClonePaymentSuccess', 'false')
                console.log('✅ 用户从未购买过克隆服务，hasSeenVoiceClonePaymentSuccess 设置为 false')
            } else {
                // 用户之前已购买过克隆服务，设置为 true（已看过弹窗）
                localStorage.setItem('hasSeenVoiceClonePaymentSuccess', 'true')
                console.log('✅ 用户之前已购买过克隆服务，hasSeenVoiceClonePaymentSuccess 设置为 true')
            }
        } else {
            // 没有会员信息，默认设置为 false
            localStorage.setItem('hasSeenVoiceClonePaymentSuccess', 'false')
            console.log('⚠️  没有会员信息，hasSeenVoiceClonePaymentSuccess 默认设置为 false')
        }
        
        console.log('当前 hasSeenVoiceClonePaymentSuccess 值:', localStorage.getItem('hasSeenVoiceClonePaymentSuccess'))
        console.log('=== 初始化支付成功弹窗状态完成 ===')
        
    } catch (error) {
        console.error('❌ 初始化支付成功弹窗状态失败:', error)
        // 出错时默认设置为 false，确保不会阻止应有的弹窗显示
        localStorage.setItem('hasSeenVoiceClonePaymentSuccess', 'false')
        console.log('出错时默认设置 hasSeenVoiceClonePaymentSuccess 为 false')
    }
}

// 初始加载数据
onMounted(async () => {
    // 初始化支付成功弹窗状态
    await initializePaymentSuccessDialogStatus()
    
    // 加载声音列表
    fetchVoiceList()

    // 添加全局点击事件监听器，点击其他区域时停止播放
    document.addEventListener('click', handleGlobalClick)
})

onUnmounted(() => {
    // 清理全局事件监听器
    document.removeEventListener('click', handleGlobalClick)
})

// 全局点击事件处理函数
const handleGlobalClick = (event) => {
    // 检查点击的元素是否是播放按钮或其子元素
    const isPlayButton = event.target.closest('.play-img') ||
                        event.target.closest('.play-button-item') ||
                        event.target.classList.contains('play-img') ||
                        event.target.classList.contains('play-button-item')

    // 如果不是播放按钮，则停止所有音频播放
    if (!isPlayButton) {
        stopAllAudio()
    }
}

// 停止所有播放的通用方法
const stopAllAudio = () => {
    // 停止所有音频元素
    Object.keys(audioElements.value).forEach(key => {
        if (audioElements.value[key]) {
            audioElements.value[key].pause()
            audioElements.value[key] = null
        }
    })

    // 重置所有播放状态
    // 快速克隆状态
    isQuick1Playing1.value = false
    isQuick1Playing2.value = false
    isQuick2Playing1.value = false
    isQuick2Playing2.value = false

    // 精品克隆状态
    isPremium1Playing1.value = false
    isPremium1Playing2.value = false
    isPremium2Playing1.value = false
    isPremium2Playing2.value = false

    // SFT克隆状态
    isSft1Playing1.value = false
    isSft1Playing2.value = false
    isSft2Playing1.value = false
    isSft2Playing2.value = false
}

// 快速克隆播放功能方法
const playQuickAudio = (containerIndex, buttonNumber) => {
    // 根据容器和按钮获取对应的音频URL
    const getQuickAudioUrl = (container, button) => {
        const audioUrls = {
            // 王也的音频
            '1_1': 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E7%8E%8B%E4%B9%9F%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3', // 王也本人声音
            '1_2': 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E7%8E%8B%E4%B9%9F%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3', // 王也复刻声音
            // 顾知夏的音频
            '2_1': 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E9%A1%BE%E7%9F%A5%E5%A4%8F%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3', // 顾知夏本人声音
            '2_2': 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E9%A1%BE%E7%9F%A5%E5%A4%8F%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3'  // 顾知夏复刻声音
        }
        return audioUrls[`${container}_${button}`] || 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
    }

    const audioUrl = getQuickAudioUrl(containerIndex, buttonNumber)

    // 获取对应的状态变量
    const stateVar = getQuickPlayingState(containerIndex, buttonNumber)

    if (stateVar.value) {
        // 如果正在播放，则停止当前播放
        stopQuickAudio(containerIndex, buttonNumber)
    } else {
        // 停止所有其他播放，开始播放当前按钮
        stopAllAudio()
        stateVar.value = true
        playQuickAudioFile(audioUrl, containerIndex, buttonNumber)
    }
}

// 获取快速克隆播放状态变量的辅助函数
const getQuickPlayingState = (containerIndex, buttonNumber) => {
    if (containerIndex === 1 && buttonNumber === 1) return isQuick1Playing1
    if (containerIndex === 1 && buttonNumber === 2) return isQuick1Playing2
    if (containerIndex === 2 && buttonNumber === 1) return isQuick2Playing1
    if (containerIndex === 2 && buttonNumber === 2) return isQuick2Playing2
    throw new Error(`Invalid quick playing state: container ${containerIndex}, button ${buttonNumber}`)
}

// 停止快速克隆特定播放按钮
const stopQuickAudio = (containerIndex, buttonNumber) => {
    const key = `quick${containerIndex}_${buttonNumber}`
    if (audioElements.value[key]) {
        audioElements.value[key].pause()
        audioElements.value[key] = null
    }

    // 重置对应的状态变量
    const stateVar = getQuickPlayingState(containerIndex, buttonNumber)
    stateVar.value = false
}

// 快速克隆播放音频文件
const playQuickAudioFile = (url, containerIndex, buttonNumber) => {
    const key = `quick${containerIndex}_${buttonNumber}`

    // 创建音频元素
    audioElements.value[key] = new Audio(url)

    // 播放结束时重置状态
    audioElements.value[key].addEventListener('ended', () => {
        const stateVar = getQuickPlayingState(containerIndex, buttonNumber)
        stateVar.value = false
        audioElements.value[key] = null
    })

    // 播放音频
    audioElements.value[key].play().catch(error => {
        console.error('快速克隆播放失败:', error)
        // 播放失败时重置状态
        const stateVar = getQuickPlayingState(containerIndex, buttonNumber)
        stateVar.value = false
        audioElements.value[key] = null
    })
}

// 精品克隆播放功能方法
const playPremiumAudio = (containerIndex, buttonNumber) => {
    // 根据容器和按钮获取对应的音频URL
    const getPremiumAudioUrl = (container, button) => {
        const audioUrls = {
            // 苏禾的音频
            '1_1': 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E8%8B%8F%E7%A6%BE%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3', // 苏禾本人声音
            '1_2': 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E8%8B%8F%E7%A6%BE%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3', // 苏禾复刻声音
            // 明远的音频
            '2_1': 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%98%8E%E8%BF%9C%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3', // 明远本人声音
            '2_2': 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%98%8E%E8%BF%9C%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3'  // 明远复刻声音
        }
        return audioUrls[`${container}_${button}`] || 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
    }

    const audioUrl = getPremiumAudioUrl(containerIndex, buttonNumber)

    // 获取对应的状态变量
    const stateVar = getPremiumPlayingState(containerIndex, buttonNumber)

    if (stateVar.value) {
        // 如果正在播放，则停止当前播放
        stopPremiumAudio(containerIndex, buttonNumber)
    } else {
        // 停止所有其他播放，开始播放当前按钮
        stopAllAudio()
        stateVar.value = true
        playPremiumAudioFile(audioUrl, containerIndex, buttonNumber)
    }
}

// 获取精品克隆播放状态变量的辅助函数
const getPremiumPlayingState = (containerIndex, buttonNumber) => {
    if (containerIndex === 1 && buttonNumber === 1) return isPremium1Playing1
    if (containerIndex === 1 && buttonNumber === 2) return isPremium1Playing2
    if (containerIndex === 2 && buttonNumber === 1) return isPremium2Playing1
    if (containerIndex === 2 && buttonNumber === 2) return isPremium2Playing2
    throw new Error(`Invalid premium playing state: container ${containerIndex}, button ${buttonNumber}`)
}

// 停止精品克隆特定播放按钮
const stopPremiumAudio = (containerIndex, buttonNumber) => {
    const key = `premium${containerIndex}_${buttonNumber}`
    if (audioElements.value[key]) {
        audioElements.value[key].pause()
        audioElements.value[key] = null
    }

    // 重置对应的状态变量
    const stateVar = getPremiumPlayingState(containerIndex, buttonNumber)
    stateVar.value = false
}

// 精品克隆播放音频文件
const playPremiumAudioFile = (url, containerIndex, buttonNumber) => {
    const key = `premium${containerIndex}_${buttonNumber}`

    // 创建音频元素
    audioElements.value[key] = new Audio(url)

    // 播放结束时重置状态
    audioElements.value[key].addEventListener('ended', () => {
        const stateVar = getPremiumPlayingState(containerIndex, buttonNumber)
        stateVar.value = false
        audioElements.value[key] = null
    })

    // 播放音频
    audioElements.value[key].play().catch(error => {
        console.error('精品克隆播放失败:', error)
        // 播放失败时重置状态
        const stateVar = getPremiumPlayingState(containerIndex, buttonNumber)
        stateVar.value = false
        audioElements.value[key] = null
    })
}

// SFT克隆播放功能方法
const playSftAudio = (containerIndex, buttonNumber) => {
    // 根据容器和按钮获取对应的音频URL
    const getSftAudioUrl = (container, button) => {
        const audioUrls = {
            // 李修远的音频
            '1_1': 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%9D%8E%E4%BF%AE%E8%BF%9C%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3', // 李修远本人声音
            '1_2': 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%9D%8E%E4%BF%AE%E8%BF%9C%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3', // 李修远复刻声音
            // 沈知遥的音频
            '2_1': 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%B2%88%E7%9F%A5%E9%81%A5%E5%8E%9F%E5%A3%B0%E6%A1%88%E4%BE%8B.mp3', // 沈知遥本人声音
            '2_2': 'https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/Video/%E6%B2%88%E7%9F%A5%E9%81%A5%E5%A4%8D%E5%88%BB%E6%A1%88%E4%BE%8B.mp3'  // 沈知遥复刻声音
        }
        return audioUrls[`${container}_${button}`] || 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav'
    }

    const audioUrl = getSftAudioUrl(containerIndex, buttonNumber)

    // 获取对应的状态变量
    const stateVar = getSftPlayingState(containerIndex, buttonNumber)

    if (stateVar.value) {
        // 如果正在播放，则停止当前播放
        stopSftAudio(containerIndex, buttonNumber)
    } else {
        // 停止所有其他播放，开始播放当前按钮
        stopAllAudio()
        stateVar.value = true
        playSftAudioFile(audioUrl, containerIndex, buttonNumber)
    }
}

// 获取SFT克隆播放状态变量的辅助函数
const getSftPlayingState = (containerIndex, buttonNumber) => {
    if (containerIndex === 1 && buttonNumber === 1) return isSft1Playing1
    if (containerIndex === 1 && buttonNumber === 2) return isSft1Playing2
    if (containerIndex === 2 && buttonNumber === 1) return isSft2Playing1
    if (containerIndex === 2 && buttonNumber === 2) return isSft2Playing2
    throw new Error(`Invalid sft playing state: container ${containerIndex}, button ${buttonNumber}`)
}

// 停止SFT克隆特定播放按钮
const stopSftAudio = (containerIndex, buttonNumber) => {
    const key = `sft${containerIndex}_${buttonNumber}`
    if (audioElements.value[key]) {
        audioElements.value[key].pause()
        audioElements.value[key] = null
    }

    // 重置对应的状态变量
    const stateVar = getSftPlayingState(containerIndex, buttonNumber)
    stateVar.value = false
}

// SFT克隆播放音频文件
const playSftAudioFile = (url, containerIndex, buttonNumber) => {
    const key = `sft${containerIndex}_${buttonNumber}`

    // 创建音频元素
    audioElements.value[key] = new Audio(url)

    // 播放结束时重置状态
    audioElements.value[key].addEventListener('ended', () => {
        const stateVar = getSftPlayingState(containerIndex, buttonNumber)
        stateVar.value = false
        audioElements.value[key] = null
    })

    // 播放音频
    audioElements.value[key].play().catch(error => {
        console.error('SFT克隆播放失败:', error)
        // 播放失败时重置状态
        const stateVar = getSftPlayingState(containerIndex, buttonNumber)
        stateVar.value = false
        audioElements.value[key] = null
    })
}

// 卡片选择方法
const selectCloneOption = (option) => {
    selectedOption.value = option
    console.log(`选择了${option}克隆选项`)
}

// 快速克隆处理方法 - 修改为切换到快速克隆视图
const handleQuickClone = () => {
    selectedOption.value = 'quick' // 设置选中状态
    currentView.value = 'quick-clone' // 切换到快速克隆视图
    console.log('切换到快速克隆页面')
}

// 精品克隆处理方法 - 修改为显示联系客服弹窗
const handlePremiumClone = () => {
    selectedOption.value = 'premium' // 设置选中状态
    console.log('开始精品克隆 - 显示联系客服弹窗')
    // 显示联系客服弹窗
    if (contactRef.value) {
        contactRef.value.dialogVisible = true
    }
}

// SFT克隆处理方法 - 修改为显示联系客服弹窗（立即咨询功能）
const handleSftClone = () => {
    selectedOption.value = 'sft' // 设置选中状态
    console.log('开始SFT克隆 - 显示联系客服弹窗（立即咨询）')
    // 显示联系客服弹窗
    if (contactRef.value) {
        contactRef.value.dialogVisible = true
    }
}

// 返回主页面
const handleBackToMain = () => {
    currentView.value = 'main'
    selectedOption.value = '' // 清除选中状态
    console.log('返回主页面')
    window.location.reload()
}

// 处理从克隆结果页面返回首页
const handleGoToHome = (options = {}) => {
    console.log('从克隆结果页面返回首页，参数:', options)

    // 切换到主页面
    currentView.value = 'main'
    selectedOption.value = '' // 清除选中状态

    // 刷新整个页面，确保所有数据和状态都是最新的
    console.log('刷新整个页面')
    window.location.reload()
}

// 处理上传音频
const handleUpload = () => {
    console.log('处理上传音频文件')
    // 这里可以添加上传音频文件的具体逻辑
}

// 处理录制音频
const handleRecord = () => {
    console.log('处理录制音频')
    // 这里可以添加录制音频的具体逻辑
}

// 下拉菜单命令处理方法
const handleDropdownCommand = (command, voice) => {
    console.log('下拉菜单命令:', command, '声音:', voice.platformNickname)

    if (command === 'ai-dubbing') {
        handleAIDubbing(voice)
    } else if (command === 'digital-human') {
        handleDigitalHuman(voice)
    }
}

// 制作AI配音处理方法
const handleAIDubbing = (voice) => {
    console.log('制作AI配音:', voice.platformNickname)

    // 将选中的克隆声音数据存储到soundStore中
    soundStore.setCloneData(voice)

    // 跳转到AI配音页面，并传递参数clone=true
    router.push({
        path: '/AIDubbing',
        query: {
            clone: true,
            voiceId: voice.id
        }
    })
}

// 制作数字人视频处理方法
const handleDigitalHuman = (voice) => {
    console.log('制作数字人视频:', voice.platformNickname)

    // 将选中的克隆声音数据存储到soundStore中
    soundStore.setCloneData(voice)

    // 跳转到数字人编辑器页面，并传递声音参数
    router.push({
        path: '/digital-human-editor-page',
        query: {
            clone: true,
            voiceId: voice.id,
            from: '/cloneService' // 记录来源页面用于返回
        }
    })
}

// 获取克隆方式名称
const getCloneTypeName = (cloneType) => {
    // 根据cloneType的值返回相应的名称
    switch (Number(cloneType)) {
        case 1:
            return '上传文件'
        case 2:
            return '录制音频'
        default:
            return '未知克隆方式'
    }
}




</script>

<style scoped lang="scss">
.voice-clone-container {
    width: 1602px;
    margin: 0 auto;
    height: 100vh;
    overflow: hidden;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    padding: 0 20px 0;
}

/* 快速入口标题样式 */
.quick-entry-title {
    margin: 20px 0 10px 0;
    display: flex;
    align-items: center;
}

.quick-entry-title h2 {
    font-weight: 500;
    color: #333;
    margin: 0;
    font-size: 16px;
}

.quick-entry-title h2 span{
    font-size: 16px;
    color: rgba(0, 0, 0, 0.45);
    margin-left: 15px;
}

/* 克隆选项卡片区域 */
.clone-options-section {
    margin-bottom: 10px;
}

.clone-options-grid {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 16px;
    width: 100%;
}

/* 克隆卡片基础样式 */
.clone-card {
    border-radius: 16px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    cursor: pointer;
    transition: all 0.3s ease;
    overflow: hidden;
    position: relative;
    width: 499.33px;
    height: 340px;
    flex-shrink: 0;
    color: white;
}

/* 快速复刻卡片 - 绿色主题 */
.quick-clone {
    background: #51D19B;
    position: relative;
    overflow: visible;
}

.quick-clone.active {
    box-shadow: 0 8px 24px rgba(81, 209, 155, 0.3);
}

.quick-clone:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(81, 209, 155, 0.15);
}

/* 快速克隆徽章图片样式 */
.quick-clone-badge {
    position: absolute;
    top: 15px;
    right: -6px;
    width: 135px;
    height: 36px;
    z-index: 1;
}

/* 快速克隆卡片内容区域 - 允许徽章超出 */
.quick-clone .card-content {
    overflow: visible;
}

/* 精品克隆卡片 - 蓝色主题 */
.premium-clone {
    background: #5DABF8;
}

.premium-clone.active {
    box-shadow: 0 8px 24px rgba(93, 171, 248, 0.3);
}

.premium-clone:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(93, 171, 248, 0.15);
}

/* SFT克隆卡片 - 橙色主题 */
.sft-clone {
    background: #FB9747;
}

.sft-clone.active {
    box-shadow: 0 8px 24px rgba(251, 151, 71, 0.3);
}

.sft-clone:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(251, 151, 71, 0.15);
}

/* 卡片内容样式 */
.card-content {
    padding: 19px 24px 24px 24px;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

/* 卡片头部样式 */
.card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.card-title-section {
    display: flex;
    flex-direction: column;
}

.card-title {
    font-size: 23px;
    font-weight: 700;
    color: #FFFFFF;
    margin: 0;
}

.card-subtitle {
    font-size: 14px;
    font-weight: 400;
    color: #FFFFFF;
    margin: 0;
}

/* 卡片详情容器样式 */
.card-detail-container {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    width: 445px;
}

.card-detail {
    font-size: 12px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
    flex: 1;
    margin-right: 12px;
    max-width: calc(100% - 108px);
    // margin-bottom: 28px;
}

/* 按钮包装器样式 */
.card-detail-btn-wrapper {
    width: 96px;
    margin-right: 0;
}

/* 卡片详情按钮样式 */
.card-detail-btn {
    width: 96px;
    height: 31px;
    background-color: #FFFFFF;
    color: #51D19B;
    font-size: 14px;
    font-weight: 400;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.card-detail-btn:hover {
    background-color: #f8f9fa;
    transform: translateY(-1px);
}

.card-detail-btn:active {
    transform: translateY(0);
}

.premium-clone .card-detail-btn {
    color: #5DABF8;
}

.sft-clone .card-detail-btn {
    color: #FB9747;
}

.card-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

/* 白色容器组样式 */
.card-white-containers {
    display: flex;
    gap: 24px;
}

/* 白色容器样式 */
.card-white-container {
    width: 213px;
    min-height: 190px;
    background: #FFFFFF;
    border-radius: 12px;
    padding: 16px;
    box-sizing: border-box;
}

/* 用户信息区域样式 */
.user-info-section {
    display: flex;
    align-items: center;
    margin-left: 0.5px;
    /* 距离容器左侧16.5px，减去容器的16px padding */
    margin-bottom: 11px;
}

.user-avatar {
    width: 47px;
    height: 47px;
    border-radius: 50%;
    background: #E5E5E5;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    /* 占位符背景色 */
    margin-right: 13px;
}

/* 各角色头像背景图片 */
.wangye-avatar {
    background-image: url('https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E7%8E%8B%E4%B9%9F.png');
}

.guzhixia-avatar {
    background-image: url('https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E9%A1%BE%E7%9F%A5%E5%A4%8F.png');
}

.suhe-avatar {
    background-image: url('https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E8%8B%8F%E7%A6%BE.png');
}

.mingyuan-avatar {
    background-image: url('https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E6%98%8E%E8%BF%9C.png');
}

.lixiuyuan-avatar {
    background-image: url('https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E6%9D%8E%E4%BF%AE%E8%BF%9C.png');
}

.shenzhiyao-avatar {
    background-image: url('https://miaoyinbkt-hz.oss-cn-hangzhou.aliyuncs.com/img/%E6%B2%88%E7%9F%A5%E9%81%A5.png');
}

.user-name {
    font-size: 14px;
    font-weight: 400;
    color: #353D49;
}

.user-description {
    font-size: 12px;
    font-weight: 400;
    color: #353D49;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
    margin-left: 0.5px;
    /* 与头像区域对齐 */
    margin-bottom: 11px;
}

/* 播放按钮组样式 */
.play-buttons {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 25px;
    /* 调整容器高度以容纳文字标签 */
    height: auto;
    min-height: 50px;
}

/* 播放按钮项样式 */
.play-button-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

/* 播放标签文字样式 */
.play-label {
    font-size: 12px;
    font-weight: 400;
    color: #353D49;
    text-align: center;
    white-space: nowrap;
}

.play-btn {
    background: #F5F5F5;
    border: 1px solid #E0E0E0;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: 400;
    color: #353D49;
    cursor: pointer;
    transition: all 0.2s ease;
}

.play-btn:first-child {
    margin-right: 25px;
}

.play-btn:hover {
    background: #EEEEEE;
    border-color: #D0D0D0;
}

/* 播放图片样式 */
.play-img {
    cursor: pointer;
    display: block;
    /* 固定尺寸防止布局移动 - 匹配实际图片尺寸35x35 */
    width: 35px;
    height: 35px;
    /* 保持图片比例 */
    object-fit: contain;
    /* 平滑过渡效果 */
    transition: transform 0.2s ease;
    /* 防止图片拖拽 */
    user-select: none;
    -webkit-user-drag: none;
    /* 确保图片不会溢出容器 */
    max-width: 100%;
    max-height: 100%;
}

/* 移除margin-right，现在使用gap控制间距 */

/* 添加悬停效果 */
.play-img:hover {
    transform: scale(1.05);
}

/* 添加点击效果 */
.play-img:active {
    transform: scale(0.95);
}

/* 卡片描述样式 */
.card-description {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.5;
    margin-bottom: 24px;
}

/* 头像区域样式 */
.card-avatars {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.avatar-group {
    display: flex;
    gap: 8px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    font-weight: 500;
}

/* 操作按钮样式 */
.avatar-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 8px 16px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
}

/* 声音列表区域样式 */
.voice-list-section {
    background: #E9F8F200;
    border-radius: 12px;
    overflow: hidden;
    width: 100%;
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
}

.section-header {
    padding: 20px 0;
    display: flex;
    align-items: center;
}

.section-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin: 0;
}

.section-subtitle {
    font-size: 14px;
    color: #999;
}

/* 表格容器样式 */
.voice-table-container {
    padding: 0;
    width: 100%;
    flex: 1;
    overflow: visible;
    min-height: 0;
}

.voice-table {
    width: 100%;
    table-layout: auto;
}

.voice-table :deep(.el-table__row) {
    height: 48px; // 设置统一的行高
}

.voice-table :deep(.el-table__header) {
    background-color: #F2F2F2;
}

.voice-table :deep(.el-table__header th) {
    background-color: #F2F2F2;
    border-bottom: 1px solid #ebeef5;
    color: #909399;
    font-weight: 500;
    font-size: 13px;
}

.voice-table :deep(.el-table__row) {
    background-color: #FFFFFF !important;
}

.voice-table :deep(.el-table__body tr) {
    background-color: #FFFFFF !important;
}

.voice-table :deep(.el-table__body tr:hover) {
    background-color: #f8f9fa;
}

/* 声音名称单元格样式 */
.voice-name-cell {
    display: flex;
    align-items: center;
    gap: 12px;
}

.voice-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    overflow: hidden;

    img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    /* 添加默认头像样式 */
    &.default-avatar {
        background-color: #f0f2f5;
    }
}

.voice-name {
    font-size: 14px;
    color: #333;
    font-weight: 500;
}

.voice-time {
    font-size: 14px;
    color: #666;
}

.clone-method {
    font-size: 14px;
    color: #666;
    word-wrap: break-word;
    word-break: break-all;
}

/* 使用音色文字链接样式 */
.use-voice-link {
    font-size: 14px;
    color: #0AAF60;
    cursor: pointer;
    display: block;
    text-align: center;
    width: 100%;
    transition: color 0.2s ease;
}

.use-voice-link:hover {
    color: #0FC268;
}

/* 分页容器样式 */
.pagination-container {
    padding: 20px 0;
    display: flex;
    justify-content: center;
    background-color: #fff;
}

/* 自定义分页组件活动页码颜色 */
.pagination-container :deep(.el-pagination .el-pager li.is-active) {
    color: #0AAF60;
    border-color: #0AAF60;
}

/* 自定义分页组件下拉菜单选中项颜色 */
.pagination-container :deep(.el-select-dropdown__item.selected) {
    color: #0AAF60 !important;
    font-weight: bold;
}

/* 添加下拉菜单选中项的背景颜色 */
.pagination-container :deep(.el-select-dropdown__item.selected),
.pagination-container :deep(.el-select-dropdown__item.hover),
.pagination-container :deep(.el-select-dropdown__item:hover) {
    background-color: rgba(10, 175, 96, 0.1) !important;
}

.pagination-container :deep(.el-dropdown-menu__item.is-active),
.pagination-container :deep(.el-dropdown-menu__item:focus) {
    color: #0AAF60 !important;
}

/* 修改弹出框中的hover和选中状态 */
:deep(.el-popper.is-light .el-dropdown-menu__item.is-active),
:deep(.el-select-dropdown__item.selected) {
    color: #0AAF60 !important;
    background-color: rgba(10, 175, 96, 0.1) !important;
}

:deep(.el-select-dropdown__item:hover) {
    background-color: rgba(10, 175, 96, 0.1) !important;
}

/* 自定义使用音色下拉菜单样式 */
:deep(.el-dropdown-menu) {
    padding: 4px 0;
    border-radius: 4px;
    border: 1px solid #E4E7ED;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-dropdown-menu__item) {
    font-size: 14px;
    color: #606266;
    padding: 8px 16px;
    line-height: 22px;
    transition: all 0.2s ease;
}

:deep(.el-dropdown-menu__item:hover) {
    background-color: #f5f7fa;
    color: #0AAF60;
}

:deep(.el-dropdown-menu__item:focus) {
    background-color: #f5f7fa;
    color: #0AAF60;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .clone-options-grid {
        grid-template-columns: 1fr;
        max-width: 600px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .voice-clone-container {
        padding: 12px;
    }

    .clone-card {
        min-height: auto;
    }

    .card-content {
        padding: 20px;
    }

    .section-header {
        padding: 16px 20px;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .voice-table :deep(.el-table__cell) {
        padding: 8px;
    }

    .clone-method {
        max-width: 120px;
    }
}



</style>