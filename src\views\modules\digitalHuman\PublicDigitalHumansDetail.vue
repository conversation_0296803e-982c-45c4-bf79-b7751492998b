<template>
	<div class="public-digital-humans-detail-app">
		<!-- 顶部导航条区域 -->
		<Headbar />

		<!-- 主要内容区域 -->
		<div
			class="main-content"
			ref="mainContentRef"
			@touchstart="handleTouchStart"
			@touchmove="handleTouchMove"
			@touchend="handleTouchEnd"
		>
			<!-- 下拉刷新指示器 -->
			<div
				class="pull-refresh-indicator"
				:class="{ 'visible': pullRefreshStatus !== 'idle' }"
				:style="{ transform: `translateY(${pullDistance}px)` }"
			>
				<div class="refresh-content">
					<el-icon class="refresh-icon" :class="{ 'rotating': pullRefreshStatus === 'refreshing' }">
						<Refresh />
					</el-icon>
					<span class="refresh-text">{{ refreshText }}</span>
				</div>
			</div>

			<div class="page-header">
				<div class="header-content">
					<h1>公共数字人</h1>
					<el-button
						type="default"
						@click="goToDigitalHumanTransition"
						class="back-button"
					>
						返回
					</el-button>
				</div>
			</div>

			<div class="content-area">
				<!-- 公共数字人展示区域 -->
				<div
					class="humans-container"
					v-infinite-scroll="loadMore"
					:infinite-scroll-disabled="loading || !hasMore"
					:infinite-scroll-distance="50"
				>
					<div class="humans-grid">
						<div class="human-item" v-for="(item, index) in publicDigitalHumansList" :key="item.id">
							<div class="human-avatar" @mouseenter="showCreateVideo(index)" @mouseleave="hideCreateVideo(index)">
								<!-- 使用从API获取的数字人图片 -->
								<template v-for="(figure, figureIndex) in item.figures" :key="figureIndex">
									<img v-if="figure.type != 'circle_view'" :src="figure.cover" alt="公共数字人头像" />
								</template>
								<!-- 创建视频按钮 -->
								<div class="create-video-overlay" :class="{ show: hoveredIndex === index }" @click="navigateToDigitalHumanEditor(item)">创建视频</div>
							</div>
							<div class="human-name">{{ item.name }}</div>
						</div>
					</div>

					<!-- 加载状态提示 -->
					<div class="loading-container" v-if="loading && publicDigitalHumansList.length > 0">
						<el-icon class="loading-icon">
							<Loading />
						</el-icon>
						<span>加载中...</span>
					</div>

					<!-- 无更多数据提示 -->
					<div class="no-more-data" v-if="!hasMore && publicDigitalHumansList.length > 0">
						<span>没有更多数据了</span>
					</div>

					<!-- 空状态提示 -->
					<div class="empty-state" v-if="!loading && publicDigitalHumansList.length === 0">
						<el-empty description="暂无公共数字人数据" />
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup>
import { onMounted, ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Refresh, Loading } from '@element-plus/icons-vue'

const router = useRouter()
const goToDigitalHumanTransition = () => {
  router.push('/digital-human-transition')
}

// 导入统一的Headbar组件
import Headbar from '@/views/modules/mainPage/components/headbar/index.vue'
// 导入数字人相关API
import { getDigitalHumanList } from '@/api/digitalHumanLeftOperate.js'
import { useloginStore } from '@/stores/login'

// 获取用户ID
const loginStore = useloginStore()
const getUserId = () => {
    return loginStore.userId || ''
}

// ========================================
// 数据状态管理
// ========================================
const publicDigitalHumansList = ref([]) // 公共数字人列表数据
const loading = ref(false) // 加载状态
const currentPage = ref(1) // 当前页码
const pageSize = ref(30) // 每页数据量
const hasMore = ref(true) // 是否还有更多数据

// ========================================
// 鼠标悬停状态管理
// ========================================
const hoveredIndex = ref(null) // 当前悬停的数字人索引

// ========================================
// 下拉刷新相关状态
// ========================================
const mainContentRef = ref(null) // 主内容区域引用
const pullRefreshStatus = ref('idle') // 下拉刷新状态：idle, pulling, ready, refreshing
const pullDistance = ref(0) // 下拉距离
const startY = ref(0) // 触摸开始Y坐标
const currentY = ref(0) // 当前触摸Y坐标
const isPulling = ref(false) // 是否正在下拉

// 下拉刷新阈值
const PULL_THRESHOLD = 60 // 触发刷新的下拉距离
const MAX_PULL_DISTANCE = 100 // 最大下拉距离

// 下拉刷新文本
const refreshText = computed(() => {
    switch (pullRefreshStatus.value) {
        case 'pulling':
            return '下拉刷新'
        case 'ready':
            return '释放刷新'
        case 'refreshing':
            return '刷新中...'
        default:
            return ''
    }
})

// ========================================
// 数据加载方法
// ========================================

// 获取公共数字人列表数据（优化后的分页版本）
const getPublicDigitalHumansList = async (page = 1, isRefresh = false) => {
    try {
        loading.value = true

        const { data: { list, page_info: { total_page } } } = await getDigitalHumanList({
            page,
            size: pageSize.value,
            userId: getUserId()
        })

        // 过滤掉特定ID的数字人，与原页面保持一致
        const filteredList = list.filter(item => item.id !== '5474a829b22947c69a5d0e47d3b5bee7')

        if (isRefresh || page === 1) {
            // 刷新或首次加载，替换数据
            publicDigitalHumansList.value = filteredList
        } else {
            // 加载更多，追加数据
            publicDigitalHumansList.value = [...publicDigitalHumansList.value, ...filteredList]
        }

        // 更新分页状态
        currentPage.value = page
        hasMore.value = page < total_page

        console.log(`获取公共数字人列表成功 - 第${page}页:`, filteredList.length, '条数据')

    } catch (error) {
        console.error('获取公共数字人列表失败:', error)
        ElMessage.error('获取数据失败，请重试')
    } finally {
        loading.value = false
    }
}

// 加载更多数据
const loadMore = async () => {
    if (loading.value || !hasMore.value) return

    const nextPage = currentPage.value + 1
    await getPublicDigitalHumansList(nextPage, false)
}

// ========================================
// 下拉刷新事件处理
// ========================================

// 触摸开始
const handleTouchStart = (e) => {
    if (mainContentRef.value && mainContentRef.value.scrollTop === 0) {
        startY.value = e.touches[0].clientY
        isPulling.value = true
    }
}

// 触摸移动
const handleTouchMove = (e) => {
    if (!isPulling.value) return

    currentY.value = e.touches[0].clientY
    const deltaY = currentY.value - startY.value

    if (deltaY > 0 && mainContentRef.value && mainContentRef.value.scrollTop === 0) {
        // 阻止默认滚动行为
        e.preventDefault()

        // 计算下拉距离，添加阻尼效果
        const damping = 0.5
        pullDistance.value = Math.min(deltaY * damping, MAX_PULL_DISTANCE)

        // 更新下拉状态
        if (pullDistance.value >= PULL_THRESHOLD) {
            pullRefreshStatus.value = 'ready'
        } else {
            pullRefreshStatus.value = 'pulling'
        }
    }
}

// 触摸结束
const handleTouchEnd = async () => {
    if (!isPulling.value) return

    isPulling.value = false

    if (pullRefreshStatus.value === 'ready') {
        // 触发刷新
        pullRefreshStatus.value = 'refreshing'
        await handleRefresh()
    }

    // 重置状态
    pullRefreshStatus.value = 'idle'
    pullDistance.value = 0
}

// 处理刷新
const handleRefresh = async () => {
    try {
        // 重置分页状态
        currentPage.value = 1
        hasMore.value = true

        // 重新加载第一页数据
        await getPublicDigitalHumansList(1, true)

        ElMessage.success('刷新成功')
    } catch (error) {
        console.error('刷新失败:', error)
        ElMessage.error('刷新失败，请重试')
    }
}

// ========================================
// 生命周期
// ========================================

// 页面挂载时的初始化逻辑
onMounted(() => {
    console.log('公共数字人详情页面已加载')
    // 获取公共数字人列表（首次加载）
    getPublicDigitalHumansList(1, true)
})

// ========================================
// 鼠标悬停事件处理
// ========================================

// 显示创建视频按钮
const showCreateVideo = (index) => {
    hoveredIndex.value = index
}

// 隐藏创建视频按钮
const hideCreateVideo = (index) => {
    hoveredIndex.value = null
}

// ========================================
// 跳转到数字人编辑器
// ========================================

// 跳转到数字人编辑器页面
const navigateToDigitalHumanEditor = (item) => {
    const fromPage = router.currentRoute.value.path

    // 获取数字人的图片URL
    let digitalHumanUrl = ''
    let figuresType = ''
    if (item.figures && item.figures.length > 0) {
        // 查找非circle_view类型的图片
        const imageItem = item.figures.find(figure => figure.type !== 'circle_view')
        if (imageItem && imageItem.cover) {
            digitalHumanUrl = imageItem.cover
            figuresType = imageItem.type
        }
    }

    // 将数字人数据编码为JSON字符串传递
    const digitalHumanData = {
        id: item.id,
        name: item.name,
        figures: item.figures,
        url: digitalHumanUrl,
        figuresType: figuresType
    }

    router.push({
        path: '/digital-human-editor-page',
        query: {
            from: fromPage,
            digitalHumanId: item.id,
            digitalHumanName: item.name,
            digitalHumanData: JSON.stringify(digitalHumanData)
        }
    })
}
</script>

<style scoped lang="scss">
// 全屏应用容器
.public-digital-humans-detail-app {
	display: flex;
	flex-direction: column;
	overflow: hidden;
	background-color: #FFFFFF;
}

// 主要内容区域
.main-content {
	flex: 1;
	padding: 20px 20px 20px 78px;
	margin-top: 64px; // 为Headbar组件预留空间
	overflow-y: auto;
	background-color: #FFFFFF;
	position: relative; // 为下拉刷新指示器定位
}

// 下拉刷新指示器
.pull-refresh-indicator {
	position: absolute;
	top: -60px;
	left: 0;
	right: 0;
	height: 60px;
	display: flex;
	align-items: center;
	justify-content: center;
	background-color: #FFFFFF;
	opacity: 0;
	transition: opacity 0.3s ease;
	z-index: 10;

	&.visible {
		opacity: 1;
	}

	.refresh-content {
		display: flex;
		align-items: center;
		gap: 8px;
		color: #666;
		font-size: 14px;

		.refresh-icon {
			font-size: 18px;
			transition: transform 0.3s ease;

			&.rotating {
				animation: rotate 1s linear infinite;
			}
		}
	}
}

@keyframes rotate {
	from {
		transform: rotate(0deg);
	}
	to {
		transform: rotate(360deg);
	}
}

// 页面头部
.page-header {
	margin-bottom: 30px;

	.header-content {
		display: flex;
		align-items: center;
		gap: 40px; // 按钮与标题间距40px
	}

	h1 {
		font-family: 'Alibaba PuHuiTi 2.0', sans-serif;
		font-size: 32px;
		font-weight: 500;
		color: #333;
		margin: 0;
	}
	
	.back-button {
		// 设置按钮样式：白色背景，绿色边框和文字
		&.el-button--default {
			background-color: #FFFFFF;
			border-color: #0AAF60;
			color: #0AAF60;
			
			&:hover, &:focus {
				background-color: #f0f9f0; // 浅绿色背景
				border-color: #0AAF60;
				color: #0AAF60;
			}
		}
	}
}

// 内容区域
.content-area {
	flex: 1;
	display: flex;
	flex-direction: column;
	min-height: 0; // 确保flex子项能够正确收缩
}

// 公共数字人容器
.humans-container {
	flex: 1;
	width: 1767px !important; // 保持固定宽度
	height: 100%; // 明确设置高度
	overflow: visible; // 显示所有内容，不隐藏
	display: flex;
	flex-direction: column;
}

// 数字人网格样式
.humans-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start; // 改为从左侧开始对齐
	gap: 40px;
	width: 1767px;
	padding: 0;
}

.human-item {
	text-align: center;
	cursor: pointer;
	width: 160px; // 固定宽度，与图片宽度一致
	flex-shrink: 0; // 防止收缩
	margin-bottom: 20px;
}

.human-avatar {
	position: relative;
	width: 160px;
	height: auto;
	aspect-ratio: 1/1.3;
	border-radius: 8px;
	overflow: hidden;
	background: #f0f0f0;

	img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

// 创建视频按钮样式
.create-video-overlay {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 84px;
	height: 32px;
	background: #0AAF60;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 14px;
	font-weight: 500;
	border-radius: 4px;
	opacity: 0;
	visibility: hidden;
	transition: all 0.3s ease;
	cursor: pointer;
}

.create-video-overlay.show {
	opacity: 1;
	visibility: visible;
}

.human-name {
	font-size: 14px;
	color: #333;
	font-weight: 500;
	height: 24px;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 8px;
}

// 加载状态容器
.loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
	color: #666;
	font-size: 14px;
	gap: 8px;

	.loading-icon {
		font-size: 18px;
		animation: rotate 1s linear infinite;
	}
}

// 无更多数据提示
.no-more-data {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20px;
	color: #999;
	font-size: 14px;
	border-top: 1px solid #f0f0f0;
	margin-top: 20px;
}

// 空状态
.empty-state {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 60px 20px;
	min-height: 300px;
}

// 响应式设计
@media (max-width: 768px) {
	.main-content {
		padding: 15px;
		margin-top: 56px;
	}

	.page-header h1 {
		font-size: 24px;
	}
	
	.humans-container {
		width: 100% !important;
	}
	
	.humans-grid {
		width: 100% !important;
		justify-content: center;
	}
	
	.human-item {
		width: 140px;
	}
	
	.human-avatar {
		width: 140px;
	}
}
</style> 