<template>
    <el-dialog v-model="dialogDetailVisible"   ref="dialogRef" class="speech_method_dialog" :show-close="false" width="755px" append-to-body>
        <template #header>
            <div class="speech_method_dialog_header">
                <span class="speech_method_dialog_header_text">
                    请选择演讲方式
                </span>
                <div class="speech_method_dialog_header_img" @click="close">
                    <img src="@/assets/images/videoDigitalHuman/speech_method_dialog_close.svg" alt="">
                </div>
            </div>
        </template>
        <template #default>
            <div class="speech_method_dialog_content">
                <div class="speech_method_dialog_content_list">
                    <div class="speech_method_dialog_content_list_item" v-for="item in speech_method_list" :class="current_paymethod==item.value?'current':''" :key="item.value" @click="choose_speech(item)">
                        <div class="speech_method_dialog_content_list_item_text">
                            {{ item.label }}
                            <div class="speech_method_dialog_content_list_item_recommend" v-if="item.recommend">
                                推荐
                            </div>
                        </div>
                        
                    </div>
                </div>
            </div>
        </template>
    </el-dialog>
</template>
<script setup>
import { ref,defineExpose} from "vue";
let dialogDetailVisible=ref(false)
let dialogRef=ref(null)
let close=()=>{
    dialogDetailVisible.value=false
}
let speech_method_list=ref([
    {
        label:'脚本辅助字幕',
        value:1,
        recommend:true
    },{
        label:'自主演讲',
        value:2,
        recommend:false
    }
])
let current_paymethod=ref(1)
let choose_speech=(data)=>{
    current_paymethod.value=data.value
    setTimeout(()=>{
        close()
    },500)
}
let init=()=>{
    current_paymethod.value=1
}
defineExpose({
    dialogDetailVisible,
    init
})
</script>
<style lang="scss">
.speech_method_dialog{
    padding: 0;
    background: #FFFFFF;
    border-radius: 8px;
    overflow:hidden;
    .el-dialog__header{
        padding: 0;
        .speech_method_dialog_header{
            box-sizing: border-box;
            border-bottom: 1px solid #DEDEDF;
            padding:18px 16px;
            display: flex;
            align-items: center;
            .speech_method_dialog_header_text{
                font-size: 16px;
                line-height: 22px;
                color: #1D2129;
            }
            .speech_method_dialog_header_img{
                margin-left: auto;
                width: 20px;
                height: 20px;
                cursor: pointer;
                img{
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
    .el-dialog__body{
        .speech_method_dialog_content{
            padding: 32px 69px;
            width: 100%;
            box-sizing: border-box;
            .speech_method_dialog_content_list{
                display: flex;
                flex-direction: column;
                .speech_method_dialog_content_list_item{
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    width: 100%;
                    height: 163px;
                    background: #F9F9F9;
                    border-radius: 8px;
                    margin-bottom: 32px;
                    cursor: pointer;
                    .speech_method_dialog_content_list_item_text{
                        position: relative;
                        font-size: 24px;
                        line-height: 22px;
                        color: #000000;
                        .speech_method_dialog_content_list_item_recommend{
                            position: absolute;
                            right:-50px;
                            top: -27px;
                            width: 53px;
                            height: 27px;
                            box-sizing: border-box;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            background: linear-gradient(270deg, #FF0900 0%, #FF7700 100%);
                            border: 1px solid #FFD8D8;
                            border-radius: 12px 12px 12px 0px;
                            font-size: 14px;
                            line-height: 160%;
                            letter-spacing: 0.14px;
                            color: #FFFFFF;
                        }
                    }
                    &:last-child{
                        margin-bottom: 0;
                    }
                    &.current{
                        background: rgba(10, 175, 96, 0.1);
                        border: 1px solid #0AAF60;
                        .speech_method_dialog_content_list_item_text{
                            color: #0AAF60;
;
                        }
                    }
                }
            }
        }   
    }
    
}
</style>