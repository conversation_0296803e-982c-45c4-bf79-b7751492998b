# 数字人编辑器数据残留问题修复记录

## 问题描述

数字人编辑器页面存在数据残留问题，具体表现为：

1. 从"中转页"或"我的作品详情页"点击"编辑"按钮跳转到数字人编辑器时，会正确带入作品数据
2. 但是从数字人编辑器返回到中转页后，再次点击"创建数字人作品"跳转到数字人编辑器时，页面仍然显示之前的数据（字体设置、左侧配置等都在回显）

## 问题根本原因

数字人编辑器页面的数据残留问题源于**子组件状态管理机制的不完整**：

1. **左侧操作面板**的字体设置状态（`setFontStyle`、`setFontSize`、`textColor`、`borderColor`、`setThickness`）通过 `watch(() => digitalHumanStore['originalWorkData'])` 监听数据变化进行回显
2. **右侧操作面板**也有类似的监听机制来处理数据回显
3. 当从"编辑模式"返回到"新建模式"时，父组件的 `clearMiddleAreaData()` 方法会清空 store 数据，但**子组件的 watch 监听器在数据变为 null 时不会主动重置内部状态**
4. 导致子组件保留了之前编辑作品的配置状态

## 解决方案

采用**增强子组件重置能力**的方案：在子组件中添加专用的重置方法，父组件主动调用这些方法来确保完整清空。

### 技术实现

#### 1. 左侧操作面板增强

**文件**: `src/views/modules/digitalHuman/components/left_operate/index.vue`

添加 `resetSubtitleSettings()` 方法：

```javascript
/**
 * 重置字幕设置到默认值
 * 
 * 🎯 功能：将所有字体相关设置重置为默认状态，用于新建模式下的数据清空
 * 
 * 🧹 重置内容：
 * - 字体样式：重置为默认字体（微软雅黑）
 * - 字体大小：重置为18px
 * - 文字颜色：重置为白色
 * - 描边颜色：重置为空（无描边）
 * - 描边粗细：重置为0（无描边）
 */
const resetSubtitleSettings = () => {
    try {
        // 重置字体样式为默认值（空字符串表示微软雅黑）
        setFontStyle.value = '';
        
        // 重置字体大小为默认值
        setFontSize.value = 18;
        
        // 重置文字颜色为默认白色
        textColor.value = '#ffffff';
        
        // 重置描边颜色为空（无描边）
        borderColor.value = '';
        
        // 重置描边粗细为0（无描边）
        setThickness.value = 0;
        
        // 隐藏颜色选择器
        textPicker.value = false;
        borderPicker.value = false;
        
        // 通知父组件字幕样式已重置
        emitSubtitleStyleChange();
        
        console.log('✅ 左侧面板字幕设置已重置为默认值');
    } catch (error) {
        console.error('❌ 重置字幕设置失败:', error);
    }
};
```

并在 `defineExpose` 中暴露该方法：

```javascript
defineExpose({
    // ... 其他方法
    // 🔄 重置字幕设置的方法
    resetSubtitleSettings,
    // ... 其他方法
});
```

#### 2. 右侧操作面板增强

**文件**: `src/views/modules/digitalHuman/components/right_operate/index.vue`

添加 `resetPanelData()` 方法：

```javascript
/**
 * 重置右侧面板数据到默认状态
 * 
 * 🎯 功能：将右侧操作面板的所有状态重置为默认值，用于新建模式下的数据清空
 * 
 * 🧹 重置内容：
 * - 切换到输入文本模式（标签页1）
 * - 重置scheduled标记，允许重新初始化
 * - 清空子组件的内部状态
 */
const resetPanelData = async () => {
    try {
        // 重置为输入文本模式（标签页1）
        current_tab.value = 1;
        
        // 重置scheduled标记，允许重新初始化
        scheduled = false;
        
        // 等待DOM更新
        await nextTick();
        
        // 如果输入文本组件存在，尝试重置其状态
        if (text_captions_ref.value && typeof text_captions_ref.value.resetData === 'function') {
            text_captions_ref.value.resetData();
        }
        
        // 如果音频驱动组件存在，尝试重置其状态
        if (aduio_captions_ref.value && typeof aduio_captions_ref.value.resetData === 'function') {
            aduio_captions_ref.value.resetData();
        }
        
        console.log('✅ 右侧面板数据已重置为默认状态');
    } catch (error) {
        console.error('❌ 重置右侧面板数据失败:', error);
    }
};
```

并在 `defineExpose` 中暴露该方法：

```javascript
defineExpose({
    // ... 其他方法
    // 🔄 新增：重置面板数据的方法
    resetPanelData
});
```

#### 3. 父组件清空逻辑增强

**文件**: `src/views/modules/digitalHuman/DigitalHumanEditorPage.vue`

修改 `clearMiddleAreaData()` 方法，添加子组件重置调用：

```javascript
const clearMiddleAreaData = async () => {
    try {
        // ... 原有的清空逻辑 ...

        // 🔄 等待DOM更新，确保组件引用可用
        await nextTick();

        // 7. 🧹 调用左侧操作面板的重置方法，清空字体设置
        try {
            if (leftOperateRef.value && leftOperateRef.value.resetSubtitleSettings) {
                leftOperateRef.value.resetSubtitleSettings();
                console.log('✅ 已调用左侧面板字体设置重置方法');
            }
        } catch (error) {
            console.warn('⚠️ 调用左侧面板重置方法失败:', error);
        }

        // 8. 🧹 调用右侧操作面板的重置方法，清空面板状态
        try {
            if (rightOperateRef.value && rightOperateRef.value.resetPanelData) {
                await rightOperateRef.value.resetPanelData();
                console.log('✅ 已调用右侧面板数据重置方法');
            }
        } catch (error) {
            console.warn('⚠️ 调用右侧面板重置方法失败:', error);
        }

        console.log('✅ 中间预览区域数据清空完成，包括子组件状态重置');

    } catch (error) {
        console.error('❌ 清空中间预览区域数据失败:', error);
    }
};
```

## 修复效果

修复后，当用户从数字人编辑器返回到中转页，再次点击"创建数字人作品"时：

1. ✅ 字体设置完全重置为默认值（微软雅黑、18px、白色、无描边）
2. ✅ 左侧配置面板的所有选择状态被清空
3. ✅ 右侧操作面板重置为输入文本模式
4. ✅ 页面呈现为全新的空白编辑状态

## 技术特点

1. **可控性强**：父组件主动调用子组件重置方法，逻辑清晰可控
2. **风险较低**：不修改现有的监听器逻辑，只是增加新的重置能力
3. **易于维护**：重置逻辑集中管理，便于后续维护和扩展
4. **向后兼容**：不影响现有的编辑模式功能

## 测试建议

建议测试以下场景：

1. **编辑模式 → 新建模式**：
   - 从中转页点击某个作品的"编辑"按钮
   - 修改字体设置、背景等配置
   - 返回中转页
   - 点击"创建数字人作品"
   - 验证所有设置是否已重置为默认值

2. **多次切换测试**：
   - 重复上述流程多次
   - 验证每次新建时都能获得干净的初始状态

3. **编辑模式功能验证**：
   - 确保编辑现有作品时数据回显正常
   - 确保编辑模式下的所有功能不受影响
