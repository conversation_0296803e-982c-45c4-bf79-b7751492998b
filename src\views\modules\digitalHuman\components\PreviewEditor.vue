<template>
    <!-- 
    数字人预览编辑器主容器
    功能概览：
    - 数字人角色图片的显示、拖拽、缩放
    - 装饰图片的显示、拖拽、缩放  
    - 字幕文本的显示、拖拽、缩放
    - 预览窗口的智能点击选择
    - 悬停自动选中机制
    - 播放控制和时间轴事件显示
    -->
    <div class="preview-editor-container">
        <!-- Lip Sync Alert -->
        <div v-if="showLipSyncAlert" class="lip-sync-alert" @click.stop>
            <img src="@/assets/img/gantanhao.png" alt="警告" class="alert-icon" />
            <span class="alert-text">预览时暂不支持口型对齐，生成后声音会与口型匹配对齐</span>
            <span class="alert-countdown">{{ alertCountdown }}秒后自动关闭</span>
            <span class="alert-dismiss" @click.stop="dismissLipSyncAlert">不再提示</span>
        </div>

        <!-- 右键菜单 -->
        <div v-if="contextMenu.visible" class="context-menu" :style="contextMenuStyle" @click.stop>
            <div class="context-menu-item" @click="handleDeleteClick">
                <el-icon class="delete-icon">
                    <Delete />
                </el-icon>
                <span>删除</span>
            </div>
        </div>

        <!-- 
        主预览窗口
        核心功能：
        - 显示所有视觉元素（背景、图片、字幕）
        - 处理点击选择逻辑
        - 提供内容裁剪边界
        - 响应式尺寸适配
        -->
        <div class="preview-window" :style="previewWindowStyle" @click.stop="onPreviewWindowClick"
            @contextmenu="hideContextMenu">
            <!-- 
            内容裁剪层：裁剪图片内容，但不裁剪边框和控制点
            作用：确保图片内容不会超出预览窗口显示区域
            重要：边框和拉伸手柄不受此裁剪影响，始终可见
            -->
            <div class="content-clip-layer">
                <!-- 背景层：保持透明，数字人层本身会变成纯色 -->

                <!--
                可拖动背景模块（在裁剪层内，会被裁剪）
                显示条件：showBackgroundModule为true
                位置计算：基础位置 + 偏移量
                交互：通过外层边框容器处理，此层仅显示
                -->
                <div v-if="showBackgroundModule" class="background-module-content"
                    :style="backgroundModuleContentStyle">
                    <!-- 背景图片或纯色显示 -->
                    <div class="background-module-layer" :style="backgroundModuleStyle"></div>
                </div>

                <!--
                数字人角色内容（在裁剪层内，会被裁剪）
                显示条件：showCharacter为true
                内容类型：始终显示数字人图片
                位置计算：基础位置 + 偏移量
                交互：通过外层边框容器处理，此层仅显示
                -->
                <div v-if="showCharacter" class="character-image-content" :style="characterImageContentStyle">
                    <!-- 数字人图片 -->
                    <img class="character-layer" :src="props.digitalHumanConfig.url" alt="数字人角色"
                        :style="{ ...characterImageStyle, objectFit: characterImageObjectFit }" />
                </div>

                <!--
                第二个图片内容（在裁剪层内，会被裁剪）
                用途：装饰性图片或Logo
                层级：高于数字人角色，低于字幕
                尺寸：默认150x150像素
                -->
                <div v-if="showSecondImage" class="second-image-content" :style="secondImageContentStyle">
                    <img class="second-image-layer" :src="props.digitalHumanConfig.url" alt="选中的数字人"
                        :style="{ objectFit: characterImageObjectFit }" />
                </div>


            </div>

            <!-- 
            数字人角色边框和控制点（在裁剪层外，始终可见）
            核心交互层：
            - 显示选中状态边框
            - 处理拖拽操作
            - 提供拉伸控制点
            - 悬停自动选中机制
            -->
            <div v-if="showCharacter" class="character-display"
                :class="{ active: isCharacterActive && !isPlaying, hovering: isCharacterHovering && !isPlaying, playing: isPlaying }"
                :style="characterDisplayStyle" @mousedown="handleCharacterMouseDown($event)"
                @mouseenter="!isPlaying && onCharacterMouseEnter()" @mouseleave="!isPlaying && onCharacterMouseLeave()"
                @contextmenu.prevent="!isPlaying && showContextMenu($event, 'character')" data-image-type="character">
                <!-- 拉伸手柄 - 只在选中且非播放状态时显示，提供4个角的控制点 -->
                <div v-if="isCharacterActive && !isPlaying" class="resize-handles">
                    <div class="resize-handle resize-handle-tl" @mousedown.stop="startCharacterResize('tl', $event)">
                    </div>
                    <div class="resize-handle resize-handle-tr" @mousedown.stop="startCharacterResize('tr', $event)">
                    </div>
                    <div class="resize-handle resize-handle-bl" @mousedown.stop="startCharacterResize('bl', $event)">
                    </div>
                    <div class="resize-handle resize-handle-br" @mousedown.stop="startCharacterResize('br', $event)">
                    </div>
                </div>
            </div>

            <!--
            背景模块边框和控制点（在裁剪层外，始终可见）
            功能：可拖动的背景图片或纯色模块
            用途：背景图片、纯色背景等
            -->
            <div v-if="showBackgroundModule" class="background-module-display"
                :class="{ active: isBackgroundModuleActive && !isPlaying, hovering: isBackgroundModuleHovering && !isPlaying, playing: isPlaying }"
                :style="backgroundModuleDisplayStyle" @mousedown="!isPlaying && startBackgroundModuleDrag($event)"
                @mouseenter="!isPlaying && onBackgroundModuleMouseEnter()"
                @mouseleave="!isPlaying && onBackgroundModuleMouseLeave()"
                @contextmenu.prevent="!isPlaying && showContextMenu($event, 'backgroundModule')"
                data-module-type="background">
                <!-- 拉伸手柄 - 只在选中且非播放状态时显示，且仅对图案背景显示，纯色背景不显示 -->
                <div v-if="isBackgroundModuleActive && !isPlaying && backgroundConfig?.type === 'image'"
                    class="resize-handles">
                    <div class="resize-handle resize-handle-tl"
                        @mousedown.stop="startBackgroundModuleResize('tl', $event)"></div>
                    <div class="resize-handle resize-handle-tr"
                        @mousedown.stop="startBackgroundModuleResize('tr', $event)"></div>
                    <div class="resize-handle resize-handle-bl"
                        @mousedown.stop="startBackgroundModuleResize('bl', $event)"></div>
                    <div class="resize-handle resize-handle-br"
                        @mousedown.stop="startBackgroundModuleResize('br', $event)"></div>
                </div>
            </div>

            <!--
            第二个图片边框和控制点（在裁剪层外，始终可见）
            功能与数字人角色类似，但层级更高
            用途：Logo、装饰元素等辅助图片
            -->
            <div v-if="showSecondImage" class="second-image-display"
                :class="{ active: isSecondImageActive && !isPlaying, hovering: isSecondImageHovering && !isPlaying, playing: isPlaying }"
                :style="secondImageDisplayStyle" @mousedown="handleSecondImageMouseDown($event)"
                @mouseenter="!isPlaying && onSecondImageMouseEnter()"
                @mouseleave="!isPlaying && onSecondImageMouseLeave()"
                @contextmenu.prevent="!isPlaying && showContextMenu($event, 'secondImage')" data-image-type="second">
                <!-- 拉伸手柄 - 只在选中且非播放状态时显示，提供4个角的控制点 -->
                <div v-if="isSecondImageActive && !isPlaying" class="resize-handles">
                    <div class="resize-handle resize-handle-tl" @mousedown.stop="startSecondImageResize('tl', $event)">
                    </div>
                    <div class="resize-handle resize-handle-tr" @mousedown.stop="startSecondImageResize('tr', $event)">
                    </div>
                    <div class="resize-handle resize-handle-bl" @mousedown.stop="startSecondImageResize('bl', $event)">
                    </div>
                    <div class="resize-handle resize-handle-br" @mousedown.stop="startSecondImageResize('br', $event)">
                    </div>
                </div>
            </div>

            <!--
                字幕内容（在裁剪层内，会被裁剪）
                用途：显示字幕文本内容
                层级：最高层级显示
                -->
            <div v-if="showSubtitle" class="subtitle-content" :style="subtitleContentStyle"
                @click="!isPlaying && onSubtitleClick($event)">
                <span class="subtitle-text-span" :style="subtitleTextSpanStyle">{{ subtitleText }}</span>
            </div>

            <!--
            字幕边框和控制点（在裁剪层外，始终可见）
            功能：与背景层和数字人角色层保持一致的交互结构
            用途：字幕文本的边框显示和交互控制
            -->
            <div v-if="showSubtitle" class="subtitle-display"
                :class="{ active: (isSubtitleActive || isSubtitleHovering) && !isPlaying, hovering: isSubtitleHovering && !isPlaying && !isSubtitleActive, playing: isPlaying }"
                :style="subtitleDisplayStyle" @mousedown="!isPlaying && startSubtitleDrag($event)"
                @mouseenter="!isPlaying && onSubtitleMouseEnter()" @mouseleave="!isPlaying && onSubtitleMouseLeave()"
                @contextmenu.prevent="!isPlaying && showContextMenu($event, 'subtitle')">
                <!--
                字幕拉伸手柄 - 与第一层、第二层保持一致的4个角控制点
                布局：四个角控制点
                功能：保持宽高比拉伸
                播放时隐藏控制点
                悬停或选中时显示：提升用户体验和可发现性
                -->
                <div v-if="(isSubtitleActive || isSubtitleHovering) && !isPlaying" class="resize-handles">
                    <!-- 四个角控制点 - 与背景图保持一致 -->
                    <div class="resize-handle resize-handle-tl" @mousedown.stop="startSubtitleResize('tl', $event)">
                    </div>
                    <div class="resize-handle resize-handle-tr" @mousedown.stop="startSubtitleResize('tr', $event)">
                    </div>
                    <div class="resize-handle resize-handle-bl" @mousedown.stop="startSubtitleResize('bl', $event)">
                    </div>
                    <div class="resize-handle resize-handle-br" @mousedown.stop="startSubtitleResize('br', $event)">
                    </div>
                </div>
            </div>

            <!-- 
            原有的时间轴文本显示层
            用途：显示时间轴事件中的文本内容
            条件：activeText存在且不等于字幕文本
            -->
            <div class="text-overlay-layer" v-if="activeText && activeText !== subtitleText">
                {{ activeText }}
            </div>
        </div>

        <!-- 
        控制按钮区域
        功能：
        - 播放/暂停控制
        - 数字人显示/隐藏切换
        - 装饰图片显示/隐藏切换  
        - 字幕显示/隐藏切换
        -->
        <div class="controls" :style="controlsStyle">
            <img :src="playButtonImage" alt="播放控制" @click="togglePlay" class="play-control-image" />
        </div>

        <!-- 右下角时间显示 -->
        <div class="time-display-corner">
            {{ formatTime(currentTime) }}/{{ formatTime(totalDuration) }}
            <!-- 调试信息：显示原始时间值 -->
            <div v-if="false" style="font-size: 10px; color: #999; margin-top: 2px;">
                调试: currentTime={{ currentTime }}, totalDuration={{ totalDuration }}, url={{
                    props.digitalHumanConfig.url ? '有' : '无' }}
            </div>
        </div>



    </div>
</template>

<script setup>
// 导入Vue核心功能
import { computed, defineProps, ref, watch, onMounted, onUnmounted, defineEmits, nextTick } from 'vue';
// 导入数字人状态管理store
import { useDigitalHumanStore } from '../store/digitalHumanStore';
// 导入Pinia状态响应式工具
import { storeToRefs } from 'pinia';
// 导入Element Plus删除图标
import { Delete } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
// 导入时间轴组件（已注释，暂未使用）
// import TimelineTrack from './TimelineTrack.vue';
// 导入播放控制按钮图标
import bofang1 from '@/assets/img/bofang1.png';
import bofang2 from '@/assets/img/bofang2.png';
// 🎨 导入字体加载器
import FontLoader from '@/utils/fontLoader.js';

// 注册组件
const components = { Delete };

// ========================================
// 🎯 组件通信定义
// ========================================

// 定义向父组件发射的事件
const emit = defineEmits([
    'positionUpdate',    // 位置更新事件
    'characterMoved',    // 数字人角色移动事件
    'secondImageMoved',  // 装饰图片移动事件
    'subtitleMoved',     // 字幕移动事件
    'digital-human-cleared'  // 数字人清空事件
]);

// 初始化数字人状态store
const store = useDigitalHumanStore();
// 解构获取时间轴相关的响应式状态
const { timelineEvents, currentTime, totalDuration, isPlaying, activeSubtitle, isSubtitleLoaded } = storeToRefs(store);

// 组件Props定义
const props = defineProps({
    aspectRatio: {
        type: String,
        default: '9:16'  // 默认竖屏比例，适合移动端
    },
    backgroundConfig: {
        type: Object,
        default: () => ({
            type: null,
            value: null  // 默认为空，只有用户主动设置时才有值
        })
    },
    digitalHumanConfig: {
        type: Object,
        default: () => ({
            type: 'picture',
            url: '',  // 删除默认数字人图片，等待用户选择
            index: null
        })
    },
    subtitleConfig: {
        type: Object,
        default: () => ({
            fontFamily: '1',
            fontSize: 18,           // 🎯 修改：默认字号改为18
            textColor: '#ffffff',
            borderColor: '',        // 🎯 修改：默认描边色改为空
            borderWidth: 0          // 🎯 修改：默认描边粗细改为0
        })
    },
    subtitleVisible: {
        type: Boolean,
        default: true  // 默认显示字幕
    },
    inputText: {
        type: String,
        default: ''  // 用户输入的文本，用作字幕显示的备选数据源
    }
});

// ========================================
// 📊 位置数据管理方法
// ========================================

/**
 * 获取当前所有元素的位置和尺寸数据
 * 功能：收集所有可拖拽元素的完整位置信息
 * 返回：包含所有元素坐标、尺寸、显示状态的对象
 * 用途：供接口调用，保存当前布局状态
 * 
 * 【坐标系说明】
 * 坐标系原点(0,0)位于预览窗口的左上角
 * x坐标：从左到右递增，单位为像素
 * y坐标：从上到下递增，单位为像素
 * 
 * 【数据格式说明】
 * - x, y: 元素左上角在预览窗口内的绝对位置坐标(像素)
 * - width, height: 元素的宽度和高度(像素)
 * - scaleX, scaleY: 相对于初始尺寸的缩放比例(无单位，1.0表示原始大小)
 * - offsetX, offsetY: 用户拖拽产生的位置偏移量，相对于初始位置(像素)
 * - visible: 元素显示状态(布尔值)
 * - active: 元素选中状态(布尔值)
 */
const getAllPositionsData = () => {
    return {
        // 背景模块数据（第一层背景）
        backgroundModule: {
            x: backgroundModuleX.value,              // 当前X坐标
            y: backgroundModuleY.value,              // 当前Y坐标
            width: backgroundModuleWidth.value,      // 当前宽度
            height: backgroundModuleHeight.value,    // 当前高度
            scaleX: userBackgroundModuleScaleX.value, // X轴缩放比例
            scaleY: userBackgroundModuleScaleY.value, // Y轴缩放比例
            offsetX: userBackgroundModuleOffsetX.value, // 用户X偏移
            offsetY: userBackgroundModuleOffsetY.value, // 用户Y偏移
            visible: showBackgroundModule.value,     // 显示状态
            active: isBackgroundModuleActive.value   // 选中状态
        },
        // 数字人角色数据（第二层数字人）
        character: {
            x: characterX.value,              // 当前X坐标
            y: characterY.value,              // 当前Y坐标
            width: characterWidth.value,      // 当前宽度
            height: characterHeight.value,    // 当前高度
            scaleX: userCharacterScaleX.value, // X轴缩放比例
            scaleY: userCharacterScaleY.value, // Y轴缩放比例
            offsetX: userCharacterOffsetX.value, // 用户X偏移
            offsetY: userCharacterOffsetY.value, // 用户Y偏移
            visible: showCharacter.value,     // 显示状态
            active: isCharacterActive.value   // 选中状态
        },
        // 装饰图片数据（第二层数字人使用与第一层相同的数据）
        secondImage: {
            x: secondImageX.value,
            y: secondImageY.value,
            width: secondImageWidth.value,
            height: secondImageHeight.value,
            scaleX: userCharacterScaleX.value,      // 🎯 修复：使用统一的数字人数据
            scaleY: userCharacterScaleY.value,      // 🎯 修复：使用统一的数字人数据
            offsetX: userCharacterOffsetX.value,    // 🎯 修复：使用统一的数字人数据
            offsetY: userCharacterOffsetY.value,    // 🎯 修复：使用统一的数字人数据
            visible: showSecondImage.value,
            active: isSecondImageActive.value
        },
        // 字幕数据
        subtitle: {
            x: subtitleX.value,
            y: subtitleY.value,
            width: subtitleWidth.value,
            height: subtitleHeight.value,
            scaleX: userSubtitleScaleX.value,
            scaleY: userSubtitleScaleY.value,
            offsetX: userSubtitleOffsetX.value,
            offsetY: userSubtitleOffsetY.value,
            visible: showSubtitle.value,
            active: isSubtitleActive.value,
            text: subtitleText.value,         // 字幕文本内容
            fontSize: fontSize.value,         // 字体大小
            textColor: textColor.value,       // 字体颜色
            textAlign: textAlign.value,       // 文本对齐
            fontWeight: fontWeight.value      // 字体粗细
        },
        // 预览窗口信息
        previewWindow: {
            width: previewWindowWidth.value,
            height: previewWindowHeight.value,
            aspectRatio: props.aspectRatio
        },
        // 时间戳
        timestamp: Date.now()
    };
};

/**
 * 发射位置更新事件
 * 功能：向父组件发送最新的位置数据
 * 触发时机：拖拽结束、拉伸结束、主动调用
 * 
 * 【事件数据说明】
 * 发射的positionsData对象包含完整的位置信息：
 * {
 *   character: {
 *     x: number,       // 数字人角色左上角X坐标(像素)
 *     y: number,       // 数字人角色左上角Y坐标(像素)
 *     width: number,   // 数字人角色宽度(像素)
 *     height: number,  // 数字人角色高度(像素)
 *     ...其他属性
 *   },
 *   secondImage: { x, y, width, height, ... },  // 装饰图片数据
 *   subtitle: { x, y, width, height, ... },     // 字幕数据
 *   previewWindow: { width, height, ... }       // 预览窗口信息
 * }
 * 
 * 【坐标系统】
 * 所有x/y坐标均相对于预览窗口左上角(0,0)
 * 坐标值单位为像素，正X向右，正Y向下
 */
const emitPositionUpdate = () => {
    const positionsData = getAllPositionsData();
    emit('positionUpdate', positionsData);
    console.log('位置数据已更新:', positionsData);
};

/**
 * 获取当前所有元素的位置和尺寸数据（接口传输专用 - 动态标准坐标）
 * 功能：收集所有可拖拽元素的完整位置信息，并转换为对应宽高比的标准坐标系
 * 返回：包含所有元素标准坐标、尺寸、显示状态的对象
 * 用途：供接口调用，保存当前布局状态（使用标准坐标系）
 *
 * 【坐标系说明】
 * 标准坐标系原点(0,0)位于标准画布的左上角
 * - 9:16模式：1080×1920画布，x范围0-1080，y范围0-1920
 * - 16:9模式：1920×1080画布，x范围0-1920，y范围0-1080
 * x坐标：从左到右递增
 * y坐标：从上到下递增
 *
 * 【数据格式说明】
 * - x, y: 元素左上角在标准画布内的绝对位置坐标(像素)
 * - width, height: 元素在标准画布中的宽度和高度(像素)
 * - scaleX, scaleY: 相对于初始尺寸的缩放比例(无单位，1.0表示原始大小)
 * - offsetX, offsetY: 用户拖拽产生的位置偏移量，相对于初始位置(像素)
 * - visible: 元素显示状态(布尔值)
 * - active: 元素选中状态(布尔值)
 */
const getAllPositionsDataForAPI = () => {
    // 获取页面坐标数据
    const pageData = getAllPositionsData();



    // 获取当前页面预览窗口尺寸
    const pageSize = {
        width: previewWindowWidth.value,
        height: previewWindowHeight.value
    };

    console.log('🔄 开始转换坐标数据为API标准格式:', {
        页面尺寸: pageSize,
        标准尺寸: STANDARD_SIZE.value,
        宽高比: props.aspectRatio
    });

    // 🔍 详细验证：计算预期的转换比例
    const expectedScaleX = STANDARD_SIZE.value.width / pageSize.width;
    const expectedScaleY = STANDARD_SIZE.value.height / pageSize.height;

    console.log('📊 预期转换比例:', {
        scaleX: expectedScaleX.toFixed(4),
        scaleY: expectedScaleY.toFixed(4),
        说明: `页面${pageSize.width}×${pageSize.height} → 标准${STANDARD_SIZE.value.width}×${STANDARD_SIZE.value.height}`
    });

    // 🔍 验证页面数据的原始尺寸
    console.log('📋 页面原始数据验证:', {
        背景模块: pageData.backgroundModule ? {
            位置: `(${pageData.backgroundModule.x}, ${pageData.backgroundModule.y})`,
            尺寸: `${pageData.backgroundModule.width}×${pageData.backgroundModule.height}`,
            缩放: `${pageData.backgroundModule.scaleX}×${pageData.backgroundModule.scaleY}`
        } : '未显示',
        数字人: pageData.character ? {
            位置: `(${pageData.character.x}, ${pageData.character.y})`,
            尺寸: `${pageData.character.width}×${pageData.character.height}`,
            缩放: `${pageData.character.scaleX}×${pageData.character.scaleY}`
        } : '未显示'
    });

    // 转换各元素坐标为标准坐标
    const convertedData = {};

    // 🔍 背景模块数据转换验证
    if (pageData.backgroundModule) {
        const bgPageCoord = {
            x: pageData.backgroundModule.x,
            y: pageData.backgroundModule.y,
            width: pageData.backgroundModule.width,
            height: pageData.backgroundModule.height
        };

        console.log('🏞️ 背景模块转换前:', {
            页面坐标: bgPageCoord,
            缩放比例: `${pageData.backgroundModule.scaleX}×${pageData.backgroundModule.scaleY}`,
            偏移量: `(${pageData.backgroundModule.offsetX}, ${pageData.backgroundModule.offsetY})`
        });

        let bgStandardCoord = pageToStandardCoord(bgPageCoord, pageSize);

        // 🎯 16:9模式下的特殊处理：只有在用户未手动调整时才强制设置
        if (props.aspectRatio === '16:9') {
            // 检查用户是否进行了手动调整（偏移量不为0或缩放比例不为1）
            const hasUserAdjustment = pageData.backgroundModule.offsetX !== 0 ||
                pageData.backgroundModule.offsetY !== 0 ||
                pageData.backgroundModule.scaleX !== 1 ||
                pageData.backgroundModule.scaleY !== 1;

            if (!hasUserAdjustment) {
                // 用户未手动调整，强制设置为标准坐标（铺满整个标准画布）
                const standardSize = STANDARD_SIZE.value;
                bgStandardCoord = {
                    x: 0,                        // 强制X坐标为0
                    y: 0,                        // 强制Y坐标为0
                    width: standardSize.width,   // 铺满标准画布宽度
                    height: standardSize.height  // 铺满标准画布高度
                };
                console.log(`🎯 16:9模式：背景层坐标和尺寸已强制设置为(0,0,${standardSize.width},${standardSize.height})`);
            } else {
                // 用户已手动调整，保持转换后的坐标
                console.log('🎯 16:9模式：检测到用户手动调整，保持转换后的背景层坐标:', bgStandardCoord);
            }
        }

        console.log('🏞️ 背景模块转换后:', {
            标准坐标: bgStandardCoord,
            预期验证: {
                x: `${bgPageCoord.x} × ${expectedScaleX.toFixed(4)} = ${(bgPageCoord.x * expectedScaleX).toFixed(1)}`,
                y: `${bgPageCoord.y} × ${expectedScaleY.toFixed(4)} = ${(bgPageCoord.y * expectedScaleY).toFixed(1)}`,
                width: `${bgPageCoord.width} × ${expectedScaleX.toFixed(4)} = ${(bgPageCoord.width * expectedScaleX).toFixed(1)}`,
                height: `${bgPageCoord.height} × ${expectedScaleY.toFixed(4)} = ${(bgPageCoord.height * expectedScaleY).toFixed(1)}`
            }
        });

        convertedData.backgroundModule = {
            ...bgStandardCoord,
            // 🎯 确保width和height为偶数值
            width: makeEven(bgStandardCoord.width),
            height: makeEven(bgStandardCoord.height),
            scaleX: pageData.backgroundModule.scaleX,
            scaleY: pageData.backgroundModule.scaleY,
            offsetX: pageData.backgroundModule.offsetX,
            offsetY: pageData.backgroundModule.offsetY,
            visible: pageData.backgroundModule.visible,
            active: pageData.backgroundModule.active
        };
    } else {
        convertedData.backgroundModule = null;
    }

    // 🔍 数字人角色数据转换验证
    if (pageData.character) {
        const charPageCoord = {
            x: pageData.character.x,
            y: pageData.character.y,
            width: pageData.character.width,
            height: pageData.character.height
        };

        // 🔍 调试：详细输出数字人页面坐标数据
        console.log('🧑‍🎨 数字人页面坐标数据:', {
            页面坐标: charPageCoord,
            用户偏移量: {
                x: pageData.character.offsetX,
                y: pageData.character.offsetY
            },
            缩放比例: {
                x: pageData.character.scaleX,
                y: pageData.character.scaleY
            },
            显示状态: pageData.character.visible,
            选中状态: pageData.character.active
        });

        let charStandardCoord = pageToStandardCoord(charPageCoord, pageSize);

        // 🔍 调试：输出转换后的标准坐标
        console.log('🧑‍🎨 数字人标准坐标转换:', {
            页面坐标: charPageCoord,
            标准坐标: charStandardCoord,
            转换比例: {
                x: expectedScaleX.toFixed(4),
                y: expectedScaleY.toFixed(4)
            }
        });

        convertedData.character = {
            ...charStandardCoord,
            // 🎯 确保width和height为偶数值
            width: makeEven(charStandardCoord.width),
            height: makeEven(charStandardCoord.height),
            scaleX: pageData.character.scaleX,
            scaleY: pageData.character.scaleY,
            offsetX: pageData.character.offsetX,
            offsetY: pageData.character.offsetY,
            visible: pageData.character.visible,
            active: pageData.character.active
        };
    } else {
        convertedData.character = null;
    }

    // 🔍 装饰图片数据转换验证
    if (pageData.secondImage) {
        const imgPageCoord = {
            x: pageData.secondImage.x,
            y: pageData.secondImage.y,
            width: pageData.secondImage.width,
            height: pageData.secondImage.height
        };

        const imgStandardCoord = pageToStandardCoord(imgPageCoord, pageSize);

        convertedData.secondImage = {
            ...imgStandardCoord,
            scaleX: pageData.secondImage.scaleX,
            scaleY: pageData.secondImage.scaleY,
            offsetX: pageData.secondImage.offsetX,
            offsetY: pageData.secondImage.offsetY,
            visible: pageData.secondImage.visible,
            active: pageData.secondImage.active
        };
    } else {
        convertedData.secondImage = null;
    }

    // 🔍 字幕数据转换验证
    if (pageData.subtitle) {
        const subPageCoord = {
            x: pageData.subtitle.x,
            y: pageData.subtitle.y,
            width: pageData.subtitle.width,
            height: pageData.subtitle.height
        };

        const subStandardCoord = pageToStandardCoord(subPageCoord, pageSize);

        convertedData.subtitle = {
            ...subStandardCoord,
            scaleX: pageData.subtitle.scaleX,
            scaleY: pageData.subtitle.scaleY,
            offsetX: pageData.subtitle.offsetX,
            offsetY: pageData.subtitle.offsetY,
            visible: pageData.subtitle.visible,
            active: pageData.subtitle.active,
            text: pageData.subtitle.text,
            fontSize: pageData.subtitle.fontSize,
            textColor: pageData.subtitle.textColor,
            textAlign: pageData.subtitle.textAlign,
            fontWeight: pageData.subtitle.fontWeight
        };
    } else {
        convertedData.subtitle = null;
    }

    // 预览窗口信息（标准尺寸）
    convertedData.previewWindow = {
        width: STANDARD_SIZE.value.width,
        height: STANDARD_SIZE.value.height,
        aspectRatio: props.aspectRatio,
        originalPageSize: pageSize  // 保留原始页面尺寸信息
    };

    // 时间戳和元数据
    convertedData.timestamp = Date.now();
    const standardSize = STANDARD_SIZE.value;
    convertedData.coordinateSystem = `standard_${standardSize.width}x${standardSize.height}`;  // 动态标识坐标系类型

    // 🔍 最终验证：检查转换结果是否符合预期
    console.log('🎯 最终转换结果验证:', {
        背景模块: convertedData.backgroundModule ? {
            原始页面尺寸: `${pageData.backgroundModule?.width || 0}×${pageData.backgroundModule?.height || 0}`,
            转换后标准尺寸: `${convertedData.backgroundModule.width}×${convertedData.backgroundModule.height}`,
            预期标准尺寸: `${Math.round((pageData.backgroundModule?.width || 0) * expectedScaleX)}×${Math.round((pageData.backgroundModule?.height || 0) * expectedScaleY)}`,
            转换是否正确: convertedData.backgroundModule.width === Math.round((pageData.backgroundModule?.width || 0) * expectedScaleX) &&
                convertedData.backgroundModule.height === Math.round((pageData.backgroundModule?.height || 0) * expectedScaleY)
        } : '未显示',
        数字人: convertedData.character ? {
            原始页面尺寸: `${pageData.character?.width || 0}×${pageData.character?.height || 0}`,
            转换后标准尺寸: `${convertedData.character.width}×${convertedData.character.height}`,
            预期标准尺寸: `${Math.round((pageData.character?.width || 0) * expectedScaleX)}×${Math.round((pageData.character?.height || 0) * expectedScaleY)}`,
            转换是否正确: convertedData.character.width === Math.round((pageData.character?.width || 0) * expectedScaleX) &&
                convertedData.character.height === Math.round((pageData.character?.height || 0) * expectedScaleY)
        } : '未显示',
        转换比例验证: {
            scaleX: expectedScaleX.toFixed(4),
            scaleY: expectedScaleY.toFixed(4),
            页面尺寸: `${pageSize.width}×${pageSize.height}`,
            标准尺寸: `${STANDARD_SIZE.value.width}×${STANDARD_SIZE.value.height}`
        }
    });



    console.log('✅ 坐标转换完成，API标准格式数据:', convertedData);
    return convertedData;
};

/**
 * 获取特定元素的位置数据
 * @param {string} elementType - 元素类型 ('character', 'secondImage', 'subtitle')
 * @returns {object} 指定元素的位置数据，包含完整的坐标、尺寸和状态信息
 *
 * 【返回数据格式】
 * {
 *   x: number,        // 元素左上角X坐标(像素)，相对于预览窗口左上角
 *   y: number,        // 元素左上角Y坐标(像素)，相对于预览窗口左上角
 *   width: number,    // 元素宽度(像素)
 *   height: number,   // 元素高度(像素)
 *   scaleX: number,   // X轴缩放比例，1.0为原始大小
 *   scaleY: number,   // Y轴缩放比例，1.0为原始大小
 *   offsetX: number,  // 用户拖拽产生的X轴偏移量(像素)
 *   offsetY: number,  // 用户拖拽产生的Y轴偏移量(像素)
 *   visible: boolean, // 元素显示状态
 *   active: boolean   // 元素选中状态
 * }
 */
const getElementPosition = (elementType) => {
    const allData = getAllPositionsData();
    return allData[elementType] || null;
};

/**
 * 获取特定元素的位置数据（API标准坐标）
 * @param {string} elementType - 元素类型 ('character', 'secondImage', 'subtitle')
 * @returns {object} 指定元素的标准坐标位置数据
 */
const getElementPositionForAPI = (elementType) => {
    const allData = getAllPositionsDataForAPI();
    return allData[elementType] || null;
};

/**
 * 🧪 测试坐标转换逻辑的正确性
 * 功能：验证页面坐标到标准坐标的转换是否按预期工作
 */
const testCoordinateConversion = () => {
    console.log('🧪 开始测试坐标转换逻辑...');

    const pageSize = {
        width: previewWindowWidth.value,
        height: previewWindowHeight.value
    };

    // 测试用例1：9:16模式下的403×700转换
    const testPageCoord = { x: 0, y: 0, width: 403, height: 700 };
    const expectedStandardCoord = {
        x: 0,
        y: 0,
        width: Math.round(403 * (1080 / 403)), // 应该约等于1080
        height: Math.round(700 * (1920 / 700))  // 应该约等于1920
    };

    const actualStandardCoord = pageToStandardCoord(testPageCoord, pageSize);

    console.log('🧪 测试结果:', {
        输入页面坐标: testPageCoord,
        预期标准坐标: expectedStandardCoord,
        实际标准坐标: actualStandardCoord,
        转换是否正确: {
            x: actualStandardCoord.x === expectedStandardCoord.x,
            y: actualStandardCoord.y === expectedStandardCoord.y,
            width: actualStandardCoord.width === expectedStandardCoord.width,
            height: actualStandardCoord.height === expectedStandardCoord.height
        },
        页面尺寸: pageSize,
        宽高比: props.aspectRatio
    });

    return actualStandardCoord;
};

// ========================================
// 🎭 数字人角色核心状态管理
// ========================================

// 根据宽高比动态设置数字人角色初始尺寸（基于标准坐标系比例）
const getInitialCharacterSize = () => {
    const standardSize = STANDARD_SIZE.value;

    if (props.aspectRatio === '16:9') {
        // 16:9横屏模式：基于1920×1080标准坐标系
        // 数字人占标准画布高度的约93%，宽度按5:8比例计算（优化：减少宽度避免拉伸）
        const standardHeight = Math.round(standardSize.height * 0.93); // 1080 * 0.93 ≈ 1004
        const standardWidth = Math.round(standardHeight * (5 / 8)); // 1004 * 5/8 ≈ 628

        // 转换为页面坐标
        const pageSize = {
            width: previewWindowWidth.value,
            height: previewWindowHeight.value
        };

        const pageCoord = standardToPageCoord({
            x: 0, y: 0,
            width: standardWidth,
            height: standardHeight
        }, pageSize);

        return { width: pageCoord.width, height: pageCoord.height };
    } else {
        // 9:16竖屏模式：基于1080×1920标准坐标系
        // 数字人占满整个标准画布
        const standardWidth = standardSize.width;   // 1080
        const standardHeight = standardSize.height; // 1920

        // 转换为页面坐标
        const pageSize = {
            width: previewWindowWidth.value,
            height: previewWindowHeight.value
        };

        const pageCoord = standardToPageCoord({
            x: 0, y: 0,
            width: standardWidth,
            height: standardHeight
        }, pageSize);

        return { width: pageCoord.width, height: pageCoord.height };
    }
};

// 根据宽高比动态设置数字人角色初始位置（基于标准坐标系比例）
const getInitialCharacterPosition = () => {
    const standardSize = STANDARD_SIZE.value;
    const characterStandardSize = (() => {
        if (props.aspectRatio === '16:9') {
            const standardHeight = Math.round(standardSize.height * 0.93);
            const standardWidth = Math.round(standardHeight * (5 / 8)); // 优化：使用5:8比例减少宽度
            return { width: standardWidth, height: standardHeight };
        } else {
            return { width: standardSize.width, height: standardSize.height };
        }
    })();

    let standardX, standardY;

    if (props.aspectRatio === '16:9') {
        // 16:9模式：水平居中，从顶部开始
        standardX = (standardSize.width - characterStandardSize.width) / 2;
        standardY = 0;
    } else {
        // 9:16模式：从左上角开始
        standardX = 0;
        standardY = 0;
    }

    // 转换为页面坐标
    const pageSize = {
        width: previewWindowWidth.value,
        height: previewWindowHeight.value
    };

    const pageCoord = standardToPageCoord({
        x: standardX,
        y: standardY,
        width: 0, height: 0
    }, pageSize);

    // 🔍 调试：检查数字人初始位置计算
    console.log('🧑‍🎨 数字人初始位置计算:', {
        宽高比: props.aspectRatio,
        标准尺寸: standardSize,
        标准位置: { x: standardX, y: standardY },
        页面尺寸: pageSize,
        页面位置: pageCoord,
        时间戳: Date.now()
    });

    return {
        x: pageCoord.x,
        y: pageCoord.y
    };
};

// ========================================
// 📍 数字人角色位置控制系统 - 用户偏移与缩放
// ========================================

/**
 * 用户自定义的位置和尺寸偏移量（相对于默认值）
 * 
 * 【坐标系统说明】
 * - 原点(0,0)位于预览窗口左上角
 * - 正X轴向右，正Y轴向下
 * - 所有坐标单位均为像素(px)
 */

/**
 * 数字人角色的X轴位置偏移量
 * - 值为0时：角色位于初始X坐标位置
 * - 正值：向右偏移指定像素
 * - 负值：向左偏移指定像素
 * @type {Ref<number>} 以像素为单位的X轴偏移量
 */
const userCharacterOffsetX = ref(0);

/**
 * 数字人角色的Y轴位置偏移量
 * - 值为0时：角色位于初始Y坐标位置
 * - 正值：向下偏移指定像素
 * - 负值：向上偏移指定像素
 * @type {Ref<number>} 以像素为单位的Y轴偏移量
 */
const userCharacterOffsetY = ref(0);

/**
 * 数字人角色的X轴缩放比例
 * - 值为1时：保持原始宽度
 * - 大于1：放大，例如2.0表示宽度放大为原来的2倍
 * - 小于1：缩小，例如0.5表示宽度缩小为原来的一半
 * @type {Ref<number>} X轴缩放比例，无单位
 */
const userCharacterScaleX = ref(1);

/**
 * 数字人角色的Y轴缩放比例
 * - 值为1时：保持原始高度
 * - 大于1：放大，例如2.0表示高度放大为原来的2倍
 * - 小于1：缩小，例如0.5表示高度缩小为原来的一半
 * @type {Ref<number>} Y轴缩放比例，无单位
 */
const userCharacterScaleY = ref(1);

/**
 * 响应式计算数字人角色的最终尺寸和位置
 * 最终值由初始值与用户自定义偏移/缩放量组合计算得出
 */

/**
 * 数字人角色的最终宽度（像素）
 * 计算公式：初始宽度 × X轴缩放比例
 * 结果会四舍五入到整数像素值
 */
const characterWidth = computed(() => {
    const initialSize = getInitialCharacterSize();
    return Math.round(initialSize.width * userCharacterScaleX.value);
});

/**
 * 数字人角色的最终高度（像素）
 * 计算公式：初始高度 × Y轴缩放比例
 * 结果会四舍五入到整数像素值
 */
const characterHeight = computed(() => {
    const initialSize = getInitialCharacterSize();
    return Math.round(initialSize.height * userCharacterScaleY.value);
});

/**
 * 数字人角色的最终X坐标（像素）
 * 计算公式：初始X坐标 + 用户X轴偏移量
 * 原点(0,0)位于预览窗口左上角
 * 正值表示向右偏移，负值表示向左偏移
 * 结果会四舍五入到整数像素值
 */
const characterX = computed(() => {
    const initialPosition = getInitialCharacterPosition();
    const offsetValue = userCharacterOffsetX.value;
    const rawResult = initialPosition.x + offsetValue;
    const finalX = Math.round(rawResult);

    // 🔍 调试：检查偏移量是否正确更新
    console.log('🧑‍🎨 数字人X坐标计算:', {
        初始位置: initialPosition.x,
        用户偏移量: offsetValue,
        最终坐标: finalX,
        时间戳: Date.now()
    });

    return finalX;
});

/**
 * 数字人角色的最终Y坐标（像素）
 * 计算公式：初始Y坐标 + 用户Y轴偏移量
 * 原点(0,0)位于预览窗口左上角
 * 正值表示向下偏移，负值表示向上偏移
 * 结果会四舍五入到整数像素值
 */
const characterY = computed(() => {
    const initialPosition = getInitialCharacterPosition();
    const finalY = Math.round(initialPosition.y + userCharacterOffsetY.value);



    return finalY;
});

const isCharacterActive = ref(false);   // 选中状态（控制边框和手柄显示）
const showCharacter = ref(false);       // 显示/隐藏控制 - 禁用第一层，只使用第二层显示数字人

// ========================================
// 🎨 背景模块状态管理（提前声明，避免watch中访问未初始化变量）
// ========================================

// 背景模块用户偏移量（用于拖拽和缩放）
const userBackgroundModuleOffsetX = ref(0);
const userBackgroundModuleOffsetY = ref(0);
const userBackgroundModuleScaleX = ref(1);
const userBackgroundModuleScaleY = ref(1);

// 背景模块交互状态
const isBackgroundModuleActive = ref(false);   // 选中状态
const isBackgroundModuleHovering = ref(false); // 悬停状态
const showBackgroundModule = ref(false);       // 显示/隐藏控制
const backgroundModuleHoverTimer = ref(null);  // 背景模块悬停定时器

/**
 * 数字人图片样式计算
 * 功能：数字人图片的基础样式，不再处理背景问题
 * 说明：背景现在由独立的背景层处理
 */
const characterImageStyle = computed(() => {
    // 数字人图片保持原始样式，背景由独立层处理
    return {};
});

/**
 * 数字人图片object-fit属性计算
 *
 * 🎯 功能：根据宽高比动态设置图片显示模式
 * 📊 逻辑：
 * - 9:16模式：使用cover保持原有效果（填满容器，可能裁剪）
 * - 16:9模式：使用contain避免变形（完整显示，可能有空白）
 * - 其他比例：使用contain保持图片比例
 * 🔄 响应式：当宽高比变化时自动更新
 */
const characterImageObjectFit = computed(() => {
    if (props.aspectRatio === '9:16') {
        return 'cover';  // 9:16模式保持原效果
    } else if (props.aspectRatio === '16:9') {
        return 'contain';  // 16:9模式避免变形
    } else {
        return 'contain';  // 其他比例避免变形
    }
});

// 监听背景配置变化，数字人图片始终显示（三层架构）
watch(() => props.backgroundConfig, () => {
    // 数字人图片始终显示，不受背景配置影响
    // 三层架构：背景层 -> 数字人层 -> 字幕层

    // 检查背景配置是否有效
    const config = props.backgroundConfig;
    const isValidConfig = config &&
        config.type &&
        config.value &&
        config.value !== '' &&
        config.value !== null &&
        config.value !== undefined &&
        (config.type === 'color' || config.type === 'image');

    // 背景模块只有在配置有效时才显示
    showBackgroundModule.value = isValidConfig;

    // 如果背景模块被隐藏，重置相关交互状态
    if (!showBackgroundModule.value) {
        isBackgroundModuleActive.value = false;
        isBackgroundModuleHovering.value = false;
    }

    // 🎯 当背景配置类型发生变化时，重置背景模块的用户偏移和缩放
    // 特别是在纯色背景和图案背景之间切换时，确保尺寸和位置正确重置
    userBackgroundModuleOffsetX.value = 0;
    userBackgroundModuleOffsetY.value = 0;
    userBackgroundModuleScaleX.value = 1;
    userBackgroundModuleScaleY.value = 1;
}, { immediate: true, deep: true });

// ========================================
// 🎭 第二图层（数字人）状态管理
// ========================================
const isSecondImageActive = ref(false); // 选中状态
const showSecondImage = ref(false);     // 显示/隐藏控制 - 默认隐藏，等待用户选择数字人

// 监听数字人配置变化
watch(() => props.digitalHumanConfig, (newConfig) => {
    console.log('数字人配置变化:', newConfig);

    // 当有数字人URL时显示对应层级，没有时隐藏
    if (newConfig && newConfig.url && newConfig.url.trim() !== '') {
        // 🎯 根据背景类型动态切换数字人显示层级
        if (props.backgroundConfig && props.backgroundConfig.type === 'color') {
            // 背景是纯色：数字人显示在第一层并自动选中
            showCharacter.value = true;
            showSecondImage.value = false;
            isCharacterActive.value = true; // 🎯 自动选中数字人，以便显示拉伸控制点
            isSecondImageActive.value = false;
            
            // 清除其他元素的选中状态
            isSubtitleActive.value = false;
            isBackgroundModuleActive.value = false;
            
            console.log('背景是纯色，数字人显示在第一层并自动选中');
        } else {
            // 背景是图片或无背景：数字人显示在第二层
            showCharacter.value = false;
            showSecondImage.value = true;
            isCharacterActive.value = false;
            isSecondImageActive.value = false;
            
            // 清除其他元素的选中状态
            isSubtitleActive.value = false;
            isBackgroundModuleActive.value = false;
            
            console.log('背景是图片或无背景，数字人显示在第二层');
        }
    } else {
        // 没有数字人URL，隐藏所有层级
        showCharacter.value = false;
        showSecondImage.value = false;
        isCharacterActive.value = false;
        isSecondImageActive.value = false;
        console.log('隐藏所有数字人层');
    }
}, { immediate: true, deep: true });

// 🎯 新增：监听背景配置变化，动态调整数字人层级
watch(() => props.backgroundConfig, (newBgConfig) => {
    // 只有在数字人存在的情况下才调整层级
    if (props.digitalHumanConfig && props.digitalHumanConfig.url && props.digitalHumanConfig.url.trim() !== '') {
        if (newBgConfig && newBgConfig.type === 'color') {
            // 背景变为纯色：数字人切换到第一层
            if (showSecondImage.value) {
                // 保存第二层的状态到第一层，并自动选中数字人以显示拉伸控制点
                const wasSecondImageActive = isSecondImageActive.value;
                
                showCharacter.value = true;
                showSecondImage.value = false;
                isCharacterActive.value = true; // 🎯 自动选中数字人，以便显示拉伸控制点
                isSecondImageActive.value = false;
                
                // 清除其他元素的选中状态
                isSubtitleActive.value = false;
                isBackgroundModuleActive.value = false;
                
                console.log('背景切换为纯色，数字人从第二层切换到第一层并自动选中');
            } else if (showCharacter.value) {
                // 如果数字人已经在第一层，确保其被选中
                isCharacterActive.value = true;
                isSecondImageActive.value = false;
                isSubtitleActive.value = false;
                isBackgroundModuleActive.value = false;
                
                console.log('背景切换为纯色，数字人已在第一层，自动选中');
            }
        } else {
            // 背景变为图片或无背景：数字人切换到第二层
            if (showCharacter.value) {
                // 保存第一层的状态到第二层
                const wasCharacterActive = isCharacterActive.value;
                
                showCharacter.value = false;
                showSecondImage.value = true;
                isCharacterActive.value = false;
                isSecondImageActive.value = wasCharacterActive;
                
                console.log('背景切换为图片或无背景，数字人从第一层切换到第二层');
            }
        }
    }
}, { immediate: true, deep: true });

// 监听字幕样式配置变化
watch(() => props.subtitleConfig, (newConfig) => {
    console.log('🔄 字幕样式配置变化:', newConfig);
}, { immediate: true, deep: true });

// 监听宽高比变化，重置用户自定义偏移
watch(() => props.aspectRatio, (newRatio, oldRatio) => {

    // 🚨 修复：只在宽高比真正发生变化时才重置
    if (oldRatio !== newRatio) {
        console.log('✅ 宽高比真正发生变化，执行重置');

        // 重置背景模块的用户偏移
        userBackgroundModuleOffsetX.value = 0;
        userBackgroundModuleOffsetY.value = 0;
        userBackgroundModuleScaleX.value = 1;
        userBackgroundModuleScaleY.value = 1;

        // 重置数字人角色的用户偏移
        userCharacterOffsetX.value = 0;
        userCharacterOffsetY.value = 0;
        userCharacterScaleX.value = 1;
        userCharacterScaleY.value = 1;

        // 重置装饰图片的用户偏移（已与数字人数据统一，无需重复重置）
        // userSecondImageOffsetX.value = 0;
        // userSecondImageOffsetY.value = 0;
        // userSecondImageScaleX.value = 1;
        // userSecondImageScaleY.value = 1;

        // 重置字幕的用户偏移
        userSubtitleOffsetX.value = 0;
        userSubtitleOffsetY.value = 0;
        userSubtitleScaleX.value = 1;
        userSubtitleScaleY.value = 1;
    } else {
        console.log('⏭️ 宽高比未变化，跳过重置');
    }
});

// 数字人角色精细位置控制已移至上方用户偏移量统一管理

// ========================================
// 🎨 坐标系统工具函数
// ========================================

/**
 * 坐标验证工具函数
 * 功能：验证坐标值的有效性并进行边界修正
 * @param {number} value - 待验证的坐标值
 * @param {number} min - 最小允许值
 * @param {number} max - 最大允许值
 * @returns {number} 修正后的坐标值
 */
const validateCoordinate = (value, min = -Infinity, max = Infinity) => {
    if (typeof value !== 'number' || isNaN(value)) {
        console.warn('⚠️ 无效的坐标值:', value, '使用默认值0');
        return 0;
    }

    const rounded = Math.round(value);
    return Math.max(min, Math.min(max, rounded));
};

/**
 * 统一的坐标精度处理函数
 * 功能：确保所有坐标值使用相同的精度处理方式
 * @param {number} coordinate - 原始坐标值
 * @returns {number} 处理后的坐标值（四舍五入到整数）
 */
const normalizeCoordinate = (coordinate) => {
    return Math.round(coordinate || 0);
};

/**
 * 确保数值为偶数的工具函数
 * 功能：将数值转换为最接近的偶数值
 * @param {number} value - 原始数值
 * @returns {number} 偶数值
 */
const makeEven = (value) => {
    const rounded = Math.round(value || 0);
    return rounded % 2 === 0 ? rounded : rounded + 1;
};

/**
 * 统一的边界检测参数
 * 功能：为所有层级提供一致的边界检测配置
 */
const BOUNDARY_CONFIG = {
    borderSize: 10,        // 最少保留边框大小（像素）
    minElementSize: 50,    // 元素最小尺寸（像素）
    maxOffsetRatio: 0.9    // 最大偏移比例（相对于预览窗口）
};

// ========================================
// 🔄 双重坐标系统转换工具函数
// ========================================

/**
 * 动态标准坐标系统配置
 * 根据宽高比返回对应的标准尺寸
 * - 9:16模式（竖屏）：1080×1920
 * - 16:9模式（横屏）：1920×1080
 * @param {string} aspectRatio - 宽高比 ('9:16', '16:9', 等)
 * @returns {Object} 标准尺寸对象 {width, height}
 */
const getStandardSize = (aspectRatio) => {
    if (aspectRatio === '16:9') {
        return {
            width: 1920,   // 16:9横屏：宽1920
            height: 1080   // 16:9横屏：高1080
        };
    } else {
        return {
            width: 1080,   // 9:16竖屏及其他：宽1080
            height: 1920   // 9:16竖屏及其他：高1920
        };
    }
};

/**
 * 获取当前宽高比对应的标准尺寸
 * 功能：为坐标转换提供动态的标准尺寸
 */
const STANDARD_SIZE = computed(() => getStandardSize(props.aspectRatio));

/**
 * 页面坐标转换为标准坐标（动态标准尺寸）
 * 功能：将页面显示坐标转换为接口传输的标准坐标
 * @param {Object} pageCoord - 页面坐标对象 {x, y, width, height}
 * @param {Object} pageSize - 页面预览窗口尺寸 {width, height}
 * @param {Object} standardSize - 标准坐标系尺寸，默认根据当前宽高比动态获取
 * @returns {Object} 标准坐标对象 {x, y, width, height}
 */
const pageToStandardCoord = (pageCoord, pageSize, standardSize = null) => {
    // 如果没有传入标准尺寸，使用当前宽高比对应的标准尺寸
    if (!standardSize) {
        standardSize = STANDARD_SIZE.value;
    }
    // 参数验证
    if (!pageCoord || typeof pageCoord !== 'object') {
        console.warn('⚠️ 页面坐标参数无效:', pageCoord);
        return { x: 0, y: 0, width: 0, height: 0 };
    }

    if (!pageSize || typeof pageSize !== 'object' || pageSize.width <= 0 || pageSize.height <= 0) {
        console.warn('⚠️ 页面尺寸参数无效:', pageSize);
        return { x: 0, y: 0, width: 0, height: 0 };
    }

    // 边界检查：确保页面坐标在合理范围内
    const xMin = -pageSize.width;
    const xMax = pageSize.width * 2;
    const yMin = -pageSize.height;
    const yMax = pageSize.height * 2;

    const safePageCoord = {
        x: validateCoordinate(pageCoord.x || 0, xMin, xMax),
        y: validateCoordinate(pageCoord.y || 0, yMin, yMax),
        width: validateCoordinate(pageCoord.width || 0, 0, pageSize.width * 2),
        height: validateCoordinate(pageCoord.height || 0, 0, pageSize.height * 2)
    };

    // 计算缩放比例
    const scaleX = standardSize.width / pageSize.width;
    const scaleY = standardSize.height / pageSize.height;

    // 坐标转换计算
    const rawX = safePageCoord.x * scaleX;
    const rawY = safePageCoord.y * scaleY;
    const normalizedX = normalizeCoordinate(rawX);
    const normalizedY = normalizeCoordinate(rawY);

    const result = {
        x: normalizedX,
        y: normalizedY,
        width: normalizeCoordinate(safePageCoord.width * scaleX),
        height: normalizeCoordinate(safePageCoord.height * scaleY)
    };



    // 结果边界检查：确保标准坐标在合理范围内
    result.x = validateCoordinate(result.x, -standardSize.width, standardSize.width * 2);
    result.y = validateCoordinate(result.y, -standardSize.height, standardSize.height * 2);
    result.width = validateCoordinate(result.width, 0, standardSize.width * 2);
    result.height = validateCoordinate(result.height, 0, standardSize.height * 2);

    return result;
};

/**
 * 标准坐标转换为页面坐标
 * 功能：将接口的标准坐标转换为页面显示坐标
 * @param {Object} standardCoord - 标准坐标对象 {x, y, width, height}
 * @param {Object} pageSize - 页面预览窗口尺寸 {width, height}
 * @param {Object} standardSize - 标准坐标系尺寸，默认根据当前宽高比动态获取
 * @returns {Object} 页面坐标对象 {x, y, width, height}
 */
const standardToPageCoord = (standardCoord, pageSize, standardSize = null) => {
    // 如果没有传入标准尺寸，使用当前宽高比对应的标准尺寸
    if (!standardSize) {
        standardSize = STANDARD_SIZE.value;
    }
    // 🔍 参数验证
    if (!standardCoord || typeof standardCoord !== 'object') {
        console.warn('⚠️ 标准坐标参数无效:', standardCoord);
        return { x: 0, y: 0, width: 0, height: 0 };
    }

    if (!pageSize || typeof pageSize !== 'object' || pageSize.width <= 0 || pageSize.height <= 0) {
        console.warn('⚠️ 页面尺寸参数无效:', pageSize);
        return { x: 0, y: 0, width: 0, height: 0 };
    }

    // 🔍 边界检查：确保标准坐标在合理范围内
    const safeStandardCoord = {
        x: validateCoordinate(standardCoord.x || 0, -standardSize.width, standardSize.width * 2),
        y: validateCoordinate(standardCoord.y || 0, -standardSize.height, standardSize.height * 2),
        width: validateCoordinate(standardCoord.width || 0, 0, standardSize.width * 2),
        height: validateCoordinate(standardCoord.height || 0, 0, standardSize.height * 2)
    };

    // 计算缩放比例
    const scaleX = pageSize.width / standardSize.width;
    const scaleY = pageSize.height / standardSize.height;

    const result = {
        x: normalizeCoordinate(safeStandardCoord.x * scaleX),
        y: normalizeCoordinate(safeStandardCoord.y * scaleY),
        width: normalizeCoordinate(safeStandardCoord.width * scaleX),
        height: normalizeCoordinate(safeStandardCoord.height * scaleY)
    };

    // 🔍 结果边界检查：确保页面坐标在合理范围内
    result.x = validateCoordinate(result.x, -pageSize.width, pageSize.width * 2);
    result.y = validateCoordinate(result.y, -pageSize.height, pageSize.height * 2);
    result.width = validateCoordinate(result.width, 0, pageSize.width * 2);
    result.height = validateCoordinate(result.height, 0, pageSize.height * 2);


    return result;
};

// ========================================
// 🎨 背景模块位置计算函数
// ========================================

// 根据宽高比动态设置背景模块初始位置（与数字人图层保持一致）
const getInitialBackgroundModulePosition = () => {
    // 🎯 特殊处理：16:9比例下，背景模块（无论纯色还是图案）都从左上角开始铺满
    if (props.aspectRatio === '16:9') {
        return {
            x: 0,
            y: 0
        };
    }

    // 其他情况：背景模块应该与数字人图层使用相同的位置逻辑
    const position = getInitialCharacterPosition();
    return {
        x: normalizeCoordinate(position.x),
        y: normalizeCoordinate(position.y)
    };
};

// 根据宽高比动态设置背景模块初始尺寸（与数字人图层保持一致）
const getInitialBackgroundModuleSize = () => {
    // 🎯 特殊处理：16:9比例下，背景模块（无论纯色还是图案）都铺满整个预览窗口
    if (props.aspectRatio === '16:9') {
        return {
            width: normalizeCoordinate(901.333),  // 16:9模式下预览窗口的完整宽度
            height: normalizeCoordinate(507)      // 16:9模式下预览窗口的完整高度
        };
    }

    // 其他情况：背景模块应该与数字人图层使用相同的尺寸逻辑
    const size = getInitialCharacterSize();
    return {
        width: normalizeCoordinate(size.width),
        height: normalizeCoordinate(size.height)
    };
};

// 重复的watch已删除，背景配置变化由上面的watch处理

// ========================================
// 🖼️ 第二个图片（装饰图片/Logo）状态管理
// ========================================

// 根据宽高比动态设置第二层数字人图片初始位置和尺寸
const getInitialSecondImagePosition = () => {
    // 第二层数字人图片应该与第一层数字人图片位置一致
    return getInitialCharacterPosition();
};

// 根据宽高比动态设置第二层数字人图片初始尺寸
const getInitialSecondImageSize = () => {
    // 第二层数字人图片应该与第一层数字人图片尺寸一致
    return getInitialCharacterSize();
};

// ========================================
// 📍 装饰图片位置控制系统 - 用户偏移与缩放
// ========================================

/**
 * 用户自定义的位置和尺寸偏移量（装饰图片/Logo）
 * 
 * 【坐标系统说明】
 * - 原点(0,0)位于预览窗口左上角
 * - 正X轴向右，正Y轴向下
 * - 所有坐标单位均为像素(px)
 * - 装饰图片默认大小为150×150像素的正方形
 */

/**
 * 装饰图片的X轴位置偏移量
 * - 值为0时：图片位于初始X坐标位置（默认居中）
 * - 正值：向右偏移指定像素
 * - 负值：向左偏移指定像素
 * @type {Ref<number>} 以像素为单位的X轴偏移量
 */
const userSecondImageOffsetX = ref(0);

/**
 * 装饰图片的Y轴位置偏移量
 * - 值为0时：图片位于初始Y坐标位置（默认居中）
 * - 正值：向下偏移指定像素
 * - 负值：向上偏移指定像素
 * @type {Ref<number>} 以像素为单位的Y轴偏移量
 */
const userSecondImageOffsetY = ref(0);

/**
 * 装饰图片的X轴缩放比例
 * - 值为1时：保持预览窗口宽度（占满宽度）
 * - 大于1：放大，例如2.0表示宽度放大为预览窗口的2倍
 * - 小于1：缩小，例如0.5表示宽度缩小为预览窗口的一半
 * @type {import('vue').Ref<number>} X轴缩放比例，无单位
 */
const userSecondImageScaleX = ref(1);

/**
 * 装饰图片的Y轴缩放比例
 * - 值为1时：保持预览窗口高度（占满高度）
 * - 大于1：放大，例如2.0表示高度放大为预览窗口的2倍
 * - 小于1：缩小，例如0.5表示高度缩小为预览窗口的一半
 * @type {import('vue').Ref<number>} Y轴缩放比例，无单位
 */
const userSecondImageScaleY = ref(1);

/**
 * 响应式计算装饰图片的最终位置和尺寸
 * 最终值由初始值与用户自定义偏移/缩放量组合计算得出
 */

/**
 * 装饰图片的最终X坐标（像素）
 * 计算公式：初始X坐标 + 用户X轴偏移量
 * 注意：使用统一的坐标精度处理，确保与其他层级一致
 *
 * 不同宽高比下的坐标系统：
 * - 16:9比例：预览窗口宽901.333px，高507px
 * - 9:16比例：预览窗口宽403px，高700px
 * - 其他比例：预览窗口宽492px，高749px
 *
 * @returns {number} 装饰图片左上角的X坐标，四舍五入到整数像素
 */
const secondImageX = computed(() => {
    // 🎯 修复：第二层数字人使用与第一层相同的数据，确保层级切换时位置一致
    const initialPosition = getInitialSecondImagePosition();
    const finalX = initialPosition.x + userCharacterOffsetX.value;
    return normalizeCoordinate(finalX);
});

/**
 * 装饰图片的最终Y坐标（像素）
 * 计算公式：初始Y坐标 + 用户Y轴偏移量
 * 注意：使用统一的坐标精度处理，确保与其他层级一致
 *
 * 坐标系统说明：
 * - 原点(0,0)位于预览窗口左上角
 * - Y轴正方向向下，负方向向上
 *
 * @returns {number} 装饰图片左上角的Y坐标，四舍五入到整数像素
 */
const secondImageY = computed(() => {
    // 🎯 修复：第二层数字人使用与第一层相同的数据，确保层级切换时位置一致
    const initialPosition = getInitialSecondImagePosition();
    const finalY = initialPosition.y + userCharacterOffsetY.value;
    return normalizeCoordinate(finalY);
});

/**
 * 第二层数字人图片的最终宽度（像素）
 * 计算公式：初始宽度 × X轴缩放比例
 * 特点：与第一层数字人图片尺寸一致，使用统一精度处理
 *
 * @returns {number} 第二层数字人图片的宽度，四舍五入到整数像素
 */
const secondImageWidth = computed(() => {
    // 🎯 修复：第二层数字人使用与第一层相同的数据，确保层级切换时尺寸一致
    const initialSize = getInitialSecondImageSize();
    const finalWidth = initialSize.width * userCharacterScaleX.value;
    return normalizeCoordinate(finalWidth);
});

/**
 * 第二层数字人图片的最终高度（像素）
 * 计算公式：初始高度 × Y轴缩放比例
 * 特点：与第一层数字人图片尺寸一致，使用统一精度处理
 *
 * @returns {number} 第二层数字人图片的高度，四舍五入到整数像素
 */
const secondImageHeight = computed(() => {
    // 🎯 修复：第二层数字人使用与第一层相同的数据，确保层级切换时尺寸一致
    const initialSize = getInitialSecondImageSize();
    const finalHeight = initialSize.height * userCharacterScaleY.value;
    return normalizeCoordinate(finalHeight);
});

// 这些变量已移到前面定义

// 装饰图片用户偏移量已移至上方统一管理

// ========================================
// 📱 预览窗口尺寸计算（提前声明，避免初始化顺序问题）
// ========================================

/**
 * 预览窗口样式计算
 * 功能：根据宽高比自动计算预览窗口的尺寸
 * 支持比例：16:9（横屏）、9:16（竖屏）、其他自定义比例
 * 限制：在最大尺寸范围内保持比例
 */
const previewWindowStyle = computed(() => {
    const mainPanelMaxWidth = 1253;    // 主面板最大宽度
    const mainPanelMaxHeight = 799;    // 主面板最大高度

    // 🔧 响应式适配：检测屏幕高度，动态调整主面板可用高度
    const screenHeight = window.innerHeight;
    let effectiveMainPanelHeight = mainPanelMaxHeight;

    if (screenHeight <= 900) {
        effectiveMainPanelHeight = 600; // 对应CSS媒体查询的高度，统一为600px
    }

    let newWidth = 0;
    let newHeight = 0;
    let marginTop = 36; // 默认上边距

    if (props.aspectRatio === '16:9') {
        // 横屏模式：根据可用高度动态调整
        if (effectiveMainPanelHeight <= 600) {
            // 小屏幕适配：调整尺寸以适应小屏幕
            newWidth = 640;
            newHeight = 360;
            marginTop = 80; // 减少上边距
        } else {
            // 大屏幕：使用原始尺寸（恢复原有设置）
            newWidth = 901.333;
            newHeight = 507;
            marginTop = 145;
        }
    } else if (props.aspectRatio === '9:16') {
        // 竖屏模式：根据可用高度动态调整
        if (effectiveMainPanelHeight <= 600) {
            // 小屏幕适配
            newWidth = 270;
            newHeight = 480;
            marginTop = 60; // 减少上边距
        } else {
            // 大屏幕：使用原始尺寸（恢复原有设置）
            newHeight = 700;
            newWidth = 403;
            marginTop = 36;
        }
    } else if (props.aspectRatio === '1:1') {
        // 正方形模式：根据可用高度动态调整
        if (effectiveMainPanelHeight <= 600) {
            // 小屏幕适配
            newWidth = 420;
            newHeight = 420;
            marginTop = 90; // 减少上边距
        } else {
            // 大屏幕：使用原始尺寸（恢复原有设置）
            newWidth = 492;
            newHeight = 749;
            marginTop = 36;
        }
    }

    return {
        width: `${newWidth}px`,
        height: `${newHeight}px`,
        'margin-top': `${marginTop}px`,
        'margin-left': 'auto',
        'margin-right': 'auto'
    };
});

/**
 * 预览窗口尺寸数值获取
 * 用途：为边界计算提供数值，避免重复解析CSS字符串
 */
const previewWindowWidth = computed(() => {
    return parseInt(previewWindowStyle.value.width);
});

const previewWindowHeight = computed(() => {
    return parseInt(previewWindowStyle.value.height);
});

// ========================================
// 📝 文本字幕状态管理
// ========================================

// 根据宽高比动态设置字幕基础尺寸（直接基于页面坐标系比例）
const getInitialSubtitleSize = () => {
    // 🎯 直接基于当前页面窗口尺寸计算，不使用标准坐标系转换
    const pageWidth = previewWindowWidth.value;   // 例如：403px (9:16) 或 901px (16:9)
    const pageHeight = previewWindowHeight.value; // 例如：700px (9:16) 或 507px (16:9)

    if (props.aspectRatio === '16:9') {
        // 16:9横屏模式：直接基于页面窗口尺寸
        // 字幕宽度约占页面窗口宽度的50%，高度约12%
        const pageWidth_subtitle = Math.round(pageWidth * 0.5);  // 901 * 0.5 ≈ 450
        const pageHeight_subtitle = Math.round(pageHeight * 0.14); // 507 * 0.12 ≈ 61

        return { width: pageWidth_subtitle, height: pageHeight_subtitle };
    } else {
        // 9:16竖屏模式：直接基于页面窗口尺寸
        // 字幕宽度约占页面窗口宽度的90%，高度约12%
        const pageWidth_subtitle = Math.round(pageWidth * 0.9);    // 403 * 0.9 ≈ 363
        const pageHeight_subtitle = Math.round(pageHeight * 0.1);  // 700 * 0.12 ≈ 84

        return { width: pageWidth_subtitle, height: pageHeight_subtitle };
    }
};

// 根据宽高比动态设置字幕初始位置（直接基于页面坐标系，居中显示）
const getInitialSubtitlePosition = () => {
    // 🎯 直接基于页面窗口尺寸计算，不使用标准坐标系转换
    const pageWidth = previewWindowWidth.value;   // 例如：403px (9:16) 或 901px (16:9)
    const pageHeight = previewWindowHeight.value; // 例如：700px (9:16) 或 507px (16:9)

    // 获取字幕的页面尺寸
    const subtitlePageSize = getInitialSubtitleSize();

    let pageX, pageY;

    if (props.aspectRatio === '16:9') {
        // 16:9比例下居中显示，稍微向上偏移
        pageX = (pageWidth - subtitlePageSize.width) / 2;      // 水平居中
        pageY = (pageHeight - subtitlePageSize.height) / 2 - 15;  // 垂直居中向上偏移（按页面比例调整）
    } else if (props.aspectRatio === '9:16') {
        // 9:16比例下居中显示
        pageX = (pageWidth - subtitlePageSize.width) / 2;      // 水平居中
        pageY = (pageHeight - subtitlePageSize.height) / 2;    // 垂直居中
    } else {
        // 其他比例下居中显示
        pageX = (pageWidth - subtitlePageSize.width) / 2;      // 水平居中
        pageY = (pageHeight - subtitlePageSize.height) / 2;    // 垂直居中
    }

    return {
        x: Math.round(pageX),
        y: Math.round(pageY)
    };
};

// ========================================
// 📍 字幕位置控制系统 - 用户偏移与缩放
// ========================================

/**
 * 用户自定义的位置和尺寸偏移量（字幕）
 * 
 * 【坐标系统说明】
 * - 原点(0,0)位于预览窗口左上角
 * - 正X轴向右，正Y轴向下
 * - 所有坐标单位均为像素(px)
 * - 字幕动态尺寸：16:9为600px×100px，9:16为380px×80px
 */

/**
 * 字幕的X轴位置偏移量
 * - 值为0时：字幕位于初始X坐标位置（默认居中）
 * - 正值：向右偏移指定像素
 * - 负值：向左偏移指定像素
 * - 数据保存：此值将传递给后端API，用于保存字幕位置
 * @type {Ref<number>} 以像素为单位的X轴偏移量
 */
const userSubtitleOffsetX = ref(0);

/**
 * 字幕的Y轴位置偏移量
 * - 值为0时：字幕位于初始Y坐标位置（默认居中）
 * - 正值：向下偏移指定像素
 * - 负值：向上偏移指定像素
 * - 数据保存：此值将传递给后端API，用于保存字幕位置
 * @type {Ref<number>} 以像素为单位的Y轴偏移量
 */
const userSubtitleOffsetY = ref(0);

/**
 * 字幕的X轴缩放比例
 * - 值为1时：保持原始宽度（默认200px）
 * - 大于1：放大，例如2.0表示宽度放大为400px
 * - 小于1：缩小，例如0.5表示宽度缩小为100px
 * - 数据保存：此值将传递给后端API，用于保存字幕尺寸
 * @type {import('vue').Ref<number>} X轴缩放比例，无单位
 */
const userSubtitleScaleX = ref(1);

/**
 * 字幕的Y轴缩放比例
 * - 值为1时：保持原始高度（默认80px）
 * - 大于1：放大，例如2.0表示高度放大为160px
 * - 小于1：缩小，例如0.5表示高度缩小为40px
 * - 数据保存：此值将传递给后端API，用于保存字幕尺寸
 * @type {import('vue').Ref<number>} Y轴缩放比例，无单位
 */
const userSubtitleScaleY = ref(1);

// 字幕显示/隐藏控制 - 简化显示条件判断
const showSubtitle = computed(() => {
    const hasSubtitleText = subtitleText.value && subtitleText.value.trim() !== '';
    const shouldShow = props.subtitleVisible && hasSubtitleText;

    return shouldShow;
});

/**
 * 响应式计算字幕的最终位置和尺寸
 * 最终值由初始值与用户自定义偏移/缩放量组合计算得出
 * 这些计算值将用于实时渲染和API数据传输
 */

/**
 * 字幕的最终X坐标（像素）
 * 计算公式：初始X坐标 + 用户X轴偏移量
 * 注意：使用统一的坐标精度处理，确保与其他层级一致
 *
 * 坐标系统说明：
 * - 原点(0,0)位于预览窗口左上角
 * - X轴正方向向右，负方向向左
 * - 此值将作为字幕位置的API传输数据
 *
 * @returns {number} 字幕左上角的X坐标，四舍五入到整数像素
 */
const subtitleX = computed(() => {
    const initialPosition = getInitialSubtitlePosition();
    const finalX = initialPosition.x + userSubtitleOffsetX.value;
    return normalizeCoordinate(finalX);
});

/**
 * 字幕的最终Y坐标（像素）
 * 计算公式：初始Y坐标 + 用户Y轴偏移量
 * 注意：使用统一的坐标精度处理，确保与其他层级一致
 *
 * 坐标系统说明：
 * - 原点(0,0)位于预览窗口左上角
 * - Y轴正方向向下，负方向向上
 * - 此值将作为字幕位置的API传输数据
 *
 * @returns {number} 字幕左上角的Y坐标，四舍五入到整数像素
 */
const subtitleY = computed(() => {
    const initialPosition = getInitialSubtitlePosition();
    const finalY = initialPosition.y + userSubtitleOffsetY.value;
    return normalizeCoordinate(finalY);
});

/**
 * 字幕的最终宽度（像素）
 * 计算公式：动态基础宽度 × X轴缩放比例
 * 特点：使用统一的坐标精度处理，根据宽高比动态调整基础尺寸
 *
 * 注意事项：
 * - 16:9模式：基础宽度600px，适合横屏显示
 * - 9:16模式：基础宽度380px，保持原有效果
 * - 最小宽度限制为100px，确保字幕可读性
 * - 此值将作为字幕尺寸的API传输数据
 *
 * @returns {number} 字幕的宽度，四舍五入到整数像素
 */
const subtitleWidth = computed(() => {
    const baseSize = getInitialSubtitleSize();
    const finalWidth = baseSize.width * userSubtitleScaleX.value;
    return normalizeCoordinate(finalWidth);
});

/**
 * 字幕的最终高度（像素）
 * 计算公式：动态基础高度 × Y轴缩放比例
 * 特点：使用统一的坐标精度处理，根据宽高比动态调整基础尺寸
 *
 * 注意事项：
 * - 16:9模式：基础高度100px，适合横屏显示
 * - 9:16模式：基础高度80px，保持原有效果
 * - 最小高度限制为40px，确保字幕可读性
 * - 此值将作为字幕尺寸的API传输数据
 *
 * @returns {number} 字幕的高度，四舍五入到整数像素
 */
const subtitleHeight = computed(() => {
    const baseSize = getInitialSubtitleSize();
    const finalHeight = baseSize.height * userSubtitleScaleY.value;
    return normalizeCoordinate(finalHeight);
});

const isSubtitleActive = ref(false);    // 选中状态

// 文本字幕内容和样式 - 增强的字幕获取逻辑（支持多种数据源）
const subtitleText = computed(() => {
    // 获取各种数据源
    const storeSubtitle = activeSubtitle.value;
    const isLoaded = isSubtitleLoaded.value;
    const manualSubtitle = store.currentSubtitle;
    const subtitleDataArray = store.subtitleData;

    // 🔍 调试信息（仅在开发模式下输出）
    if (process.env.NODE_ENV === 'development') {
        console.log('🎬 字幕数据源状态:', {
            isLoaded,
            hasStoreSubtitle: !!storeSubtitle,
            subtitleDataCount: subtitleDataArray?.length || 0,
            hasManualSubtitle: !!manualSubtitle,
            hasInputText: !!props.inputText
        });
    }

    // 第一优先级：时间匹配的字幕（播放时使用）
    if (storeSubtitle && storeSubtitle.trim() !== '') {
        const cleanText = storeSubtitle
            .replace(/[\r\n]+/g, ' ')  // 替换换行符为空格
            .replace(/\s+/g, ' ')      // 合并多个空格为一个
            .trim();                   // 移除首尾空白

        if (process.env.NODE_ENV === 'development') {
            console.log('✅ 使用时间匹配字幕:', cleanText.substring(0, 50) + '...');
        }
        return cleanText;
    }

    // 第二优先级：从pinia store获取第一条有效字幕（仅在非播放状态时使用，避免播放时空隙显示第一句）
    if (!isPlaying.value && subtitleDataArray && Array.isArray(subtitleDataArray) && subtitleDataArray.length > 0) {
        const firstValidSubtitle = subtitleDataArray.find(item => item.text && item.text.trim() !== '');

        if (firstValidSubtitle) {
            const fallbackText = firstValidSubtitle.text
                .replace(/[\r\n]+/g, ' ')  // 替换换行符为空格
                .replace(/\s+/g, ' ')      // 合并多个空格为一个
                .trim();                   // 移除首尾空白

            if (process.env.NODE_ENV === 'development') {
                console.log('📋 使用备选字幕数据（静态显示）:', fallbackText.substring(0, 50) + '...');
            }
            return fallbackText;
        }
    }

    // 第三优先级：手动设置的字幕文本（仅在非播放状态时使用）
    if (!isPlaying.value && manualSubtitle && manualSubtitle.trim() !== '') {
        const cleanText = manualSubtitle
            .replace(/[\r\n]+/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();

        if (process.env.NODE_ENV === 'development') {
            console.log('📝 使用手动设置字幕（静态显示）:', cleanText.substring(0, 50) + '...');
        }
        return cleanText;
    }

    // 🔧 第四优先级：使用输入文本作为字幕（仅在音频生成完成后显示，避免输入时实时显示）
    if (!isPlaying.value && props.inputText && props.inputText.trim() !== '' &&
        (subtitleDataArray.length > 0 || store.ttsAudioUrl)) {
        const cleanInputText = props.inputText
            .replace(/[\r\n]+/g, ' ')
            .replace(/\s+/g, ' ')
            .trim();

        if (process.env.NODE_ENV === 'development') {
            console.log('📝 使用输入文本作为字幕（音频已生成）:', cleanInputText.substring(0, 50) + '...');
        }
        return cleanInputText;
    }

    if (process.env.NODE_ENV === 'development') {
        if (isPlaying.value) {
            console.log('⏸️ 播放中无匹配字幕，显示空白');
        } else if (props.inputText && props.inputText.trim() !== '' && !subtitleDataArray.length && !store.ttsAudioUrl) {
            console.log('⏳ 输入文本存在但音频未生成，等待音频生成后显示字幕');
        } else {
            console.log('⚠️ 所有字幕数据源都为空');
        }
    }
    return '';
});

// 字幕样式计算属性 - 使用props中的配置
const fontSize = computed(() => props.subtitleConfig.fontSize);               // 字体大小
const textColor = computed(() => props.subtitleConfig.textColor);             // 字体颜色
const textAlign = ref('center');                                              // 文本对齐方式
const fontWeight = ref('normal');                                             // 字体粗细
const borderColor = computed(() => props.subtitleConfig.borderColor);         // 描边颜色
const borderWidth = computed(() => props.subtitleConfig.borderWidth);         // 描边粗细

// 🔧 强制响应式更新 - 监听currentTime变化并强制重新计算字幕
watch(currentTime, (newTime) => {
    if (newTime > 0 && isSubtitleLoaded.value) {
        // 强制触发activeSubtitle的重新计算
        nextTick(() => {
            const currentActiveSubtitle = activeSubtitle.value;
        });
    }
}, { immediate: false });

// 🎯 新增：监听字幕容器尺寸变化，输出调试信息
watch([subtitleWidth, subtitleHeight], ([newWidth, newHeight], [oldWidth, oldHeight]) => {
    if (oldWidth !== undefined && oldHeight !== undefined) {
        console.log('🎯 字幕容器尺寸变化:', {
            旧尺寸: { width: oldWidth, height: oldHeight },
            新尺寸: { width: newWidth, height: newHeight },
            变化量: {
                width: newWidth - oldWidth,
                height: newHeight - oldHeight
            },
            时间戳: Date.now()
        });

        // 计算并输出动态布局参数
        const dynamicLayout = calculateDynamicTextLayout(newWidth, newHeight, fontSize.value);
        console.log('📐 动态布局参数:', dynamicLayout);
    }
});

// 🔧 新增：初始化验证 - 确保变量初始化顺序正确
console.log('✅ PreviewEditor组件变量初始化验证:', {
    previewWindowWidth可用: typeof previewWindowWidth !== 'undefined',
    previewWindowHeight可用: typeof previewWindowHeight !== 'undefined',
    getInitialSubtitleSize可用: typeof getInitialSubtitleSize !== 'undefined',
    时间戳: Date.now()
});

// 🎨 字体加载状态管理
const fontLoadingStatus = ref({});  // 记录各字体的加载状态
const isLoadingFont = ref(false);   // 当前是否正在加载字体

// 字体样式映射
const fontFamilyMap = {
    '1': 'Microsoft YaHei, 微软雅黑',
    '2': 'Source Han Sans CN, 思源黑体',
    '3': 'SimHei, 黑体',
    '4': 'AlimamaShuHeiTi-Bold, 阿里妈妈数黑体',
    '5': 'AlibabaPuHuiTi-Regular, 阿里巴巴普惠体',
    '6': 'PingFangSC-Regular, 苹方',
    '7': 'HarmonyOS Sans SC, 鸿蒙字体',
    '8': 'Noto Sans CJK SC, 思源黑体',
    '9': 'Source Han Serif CN, 思源宋体',
    '10': 'FZLanTingHei-R-GBK, 方正兰亭黑',
    '11': 'STHeitiSC-Light, 华文黑体',
    '12': 'KaiTi, 楷体',
    '13': 'FangSong, 仿宋',
    '14': 'LiSu, 隶书',
    '15': 'YouYuan, 幼圆'
};

/**
 * 🎨 动态加载字体
 * @param {string} fontName - 字体名称
 * @param {string} fontUrl - 字体文件URL（可选）
 * @returns {Promise<boolean>} 加载是否成功
 */
const loadFont = async (fontName, fontUrl = null) => {
    if (!fontName || fontName.trim() === '') {
        return true;
    }

    // 检查是否已经加载过
    if (fontLoadingStatus.value[fontName] === 'loaded') {
        return true;
    }

    // 防止重复加载
    if (fontLoadingStatus.value[fontName] === 'loading') {
        return false;
    }

    try {
        isLoadingFont.value = true;
        fontLoadingStatus.value[fontName] = 'loading';

        // 🎨 使用FontLoader加载字体，传递fontUrl参数
        const success = await FontLoader.loadFont(fontName, fontUrl);

        if (success) {
            fontLoadingStatus.value[fontName] = 'loaded';

            // 🔄 强制重新渲染字幕，确保字体变更立即生效
            await nextTick();

            // 触发DOM更新，强制浏览器重新计算字体
            const subtitleElements = document.querySelectorAll('.subtitle-content');
            subtitleElements.forEach(el => {
                const currentDisplay = el.style.display;
                el.style.display = 'none';
                // 强制重排
                el.offsetHeight;
                el.style.display = currentDisplay;
            });

            return true;
        } else {
            fontLoadingStatus.value[fontName] = 'failed';
            console.warn(`⚠️ 字体 "${fontName}" 加载失败，将使用回退字体`);
            return false;
        }
    } catch (error) {
        fontLoadingStatus.value[fontName] = 'failed';
        console.error(`❌ 字体 "${fontName}" 加载异常:`, error);
        return false;
    } finally {
        isLoadingFont.value = false;
    }
};
const fontFamily = computed(() => {
    // 优先使用传递的字体名称，如果没有则使用映射表
    const fontName = props.subtitleConfig.fontName;
    const fontId = props.subtitleConfig.fontFamily;
    const fontUrl = props.subtitleConfig.fontUrl;



    if (fontName) {
        // 🎨 构建完整的CSS字体栈，确保有回退字体
        let cssFont = '';

        // 根据字体名称特征添加适当的引号和回退字体
        if (fontName.includes('阿里妈妈')) {
            cssFont = `"${fontName}", "AlimamaShuHeiTi-Bold", "Microsoft YaHei", "微软雅黑", sans-serif`;
        } else if (fontName.includes('阿里巴巴')) {
            cssFont = `"${fontName}", "AlibabaPuHuiTi-Regular", "Microsoft YaHei", "微软雅黑", sans-serif`;
        } else if (fontName.includes('思源')) {
            cssFont = `"${fontName}", "Source Han Sans CN", "Noto Sans CJK SC", "Microsoft YaHei", "微软雅黑", sans-serif`;
        } else if (fontName.includes('苹方')) {
            cssFont = `"${fontName}", "PingFangSC-Regular", "Microsoft YaHei", "微软雅黑", sans-serif`;
        } else if (fontName.includes('黑体')) {
            cssFont = `"${fontName}", "SimHei", "Microsoft YaHei", "微软雅黑", sans-serif`;
        } else {
            // 通用处理：为字体名称添加引号并提供系统回退字体
            cssFont = `"${fontName}", "Microsoft YaHei", "微软雅黑", "SimHei", "Arial", sans-serif`;
        }


        return cssFont;
    }

    // 回退到映射表（向后兼容）
    const selectedFont = fontFamilyMap[fontId];
    if (!selectedFont) {
        console.warn(`字体ID ${fontId} 在映射表中未找到，使用默认字体`);
        return '"Microsoft YaHei", "微软雅黑", "SimHei", sans-serif';
    }


    return selectedFont;
});

// 🎨 监听字体配置变化，动态加载字体
watch(() => [props.subtitleConfig.fontName, props.subtitleConfig.fontFamily, props.subtitleConfig.fontUrl], async ([newFontName, newFontId, newFontUrl], [oldFontName, oldFontId, oldFontUrl] = []) => {


    // 如果有字体名称，尝试加载字体
    if (newFontName && (newFontName !== oldFontName || newFontUrl !== oldFontUrl)) {


        try {
            // 🎨 使用fontUrl参数进行动态加载
            const loadResult = await loadFont(newFontName, newFontUrl);
            if (loadResult) {
                // 🔄 延迟一点时间确保字体完全加载
                setTimeout(() => {
                    // 字体加载完成，触发界面更新
                }, 100);
            }
        } catch (error) {
            console.error(`字体 "${newFontName}" 加载过程中出错:`, error);
        }
    }
}, { immediate: true, deep: true });



// ========================================
// 🖱️ 悬停自动选中机制状态
// ========================================
const characterHoverTimer = ref(null);     // 数字人悬停定时器
const isCharacterHovering = ref(false);    // 数字人悬停状态标记
const isSecondImageHovering = ref(false);  // 第二图片悬停状态
const secondImageHoverTimer = ref(null);   // 第二图片悬停定时器
const isSubtitleHovering = ref(false);     // 字幕悬停状态
const subtitleHoverTimer = ref(null);      // 字幕悬停定时器

// ========================================
// 🚀 拖拽状态管理（由统一拖拽系统管理）
// ========================================
// 注意：拖拽状态变量现在由 createUnifiedDragHandlers 函数内部管理
// 每个图层的拖拽处理器都有独立的状态管理，确保不会互相干扰

// ========================================
// 🔄 拉伸状态管理
// ========================================
// 数字人角色拉伸相关
let isResizingCharacter = false;           // 拉伸进行中标记
let resizeDirection = '';                  // 拉伸方向 (tl/tr/bl/br)
let resizeStartX = 0;                      // 拉伸起始X坐标
let resizeStartY = 0;                      // 拉伸起始Y坐标
let initialCharacterWidth = 0;             // 拉伸开始时的初始宽度
let initialCharacterHeight = 0;            // 拉伸开始时的初始高度
let initialResizeCharacterX = 0;           // 拉伸开始时的初始X位置
let initialResizeCharacterY = 0;           // 拉伸开始时的初始Y位置

// 第二个图片拉伸相关
let isResizingSecondImage = false;         // 拉伸状态标记
let secondResizeDirection = '';            // 拉伸方向
let secondResizeStartX = 0;                // 起始X坐标
let secondResizeStartY = 0;                // 起始Y坐标
let initialSecondImageWidth = 0;           // 初始宽度
let initialSecondImageHeight = 0;          // 初始高度
let initialResizeSecondImageX = 0;         // 初始X位置
let initialResizeSecondImageY = 0;         // 初始Y位置

// 字幕拉伸相关
let isResizingSubtitle = false;            // 拉伸状态标记
let subtitleResizeDirection = '';          // 拉伸方向（支持6个方向）
let subtitleResizeStartX = 0;              // 起始X坐标
let subtitleResizeStartY = 0;              // 起始Y坐标
let initialSubtitleWidth = 0;              // 初始宽度
let initialSubtitleHeight = 0;             // 初始高度
let initialResizeSubtitleX = 0;            // 初始X位置
let initialResizeSubtitleY = 0;            // 初始Y位置



// ========================================
// 🎯 选择容器扩展计算
// ========================================
/**
 * 外层选择框容器样式
 * 核心功能：扩大操作区域，允许元素拖拽到预览窗口外部
 * 扩展策略：
 * - 水平扩展：左右各扩展100%预览宽度
 * - 垂直扩展：上下各扩展50%预览高度
 * - 居中定位：确保预览窗口在扩展区域的中心
 */
const selectionContainerStyle = computed(() => {
    const previewStyle = previewWindowStyle.value;
    const previewWidth = parseInt(previewStyle.width);
    const previewHeight = parseInt(previewStyle.height);

    // 扩展比例配置
    const horizontalExtensionPercent = 1.0;  // 左右各扩展100%
    const verticalExtensionPercent = 0.5;    // 上下各扩展50%

    const horizontalExtension = previewWidth * horizontalExtensionPercent;
    const verticalExtension = previewHeight * verticalExtensionPercent;

    const expandedWidth = previewWidth + (horizontalExtension * 2);   // 总宽度 = 原宽度 + 左右扩展
    const expandedHeight = previewHeight + (verticalExtension * 2);   // 总高度 = 原高度 + 上下扩展

    return {
        position: 'absolute',
        top: `${36 - verticalExtension}px`,     // 向上偏移，为元素提供更多上方空间
        left: '50%',
        transform: 'translateX(-50%)',          // 水平居中
        width: `${expandedWidth}px`,
        height: `${expandedHeight}px`,
        pointerEvents: 'none',                  // 允许点击穿透到下层
        zIndex: 50,                             // 高层级确保选择框可见
        overflow: 'visible'                     // 允许内容溢出显示
    };
});

// ========================================
// 📍 外层选择框位置计算
// ========================================
/**
 * 数字人角色外层选择框位置计算
 * 作用：将预览窗口内的相对位置转换为扩展容器内的绝对位置
 */
const characterOuterX = computed(() => {
    const horizontalExtension = previewWindowWidth.value * 1.0;
    return characterX.value + horizontalExtension;
});
const characterOuterY = computed(() => {
    const verticalExtension = previewWindowHeight.value * 0.5;
    return characterY.value + verticalExtension;
});

/**
 * 第二个图片外层选择框位置计算
 */
const secondImageOuterX = computed(() => {
    const horizontalExtension = previewWindowWidth.value * 1.0;
    return secondImageX.value + horizontalExtension;
});
const secondImageOuterY = computed(() => {
    const verticalExtension = previewWindowHeight.value * 0.5;
    return secondImageY.value + verticalExtension;
});

/**
 * 文本字幕外层选择框位置计算
 */
const subtitleOuterX = computed(() => {
    const horizontalExtension = previewWindowWidth.value * 1.0;
    return subtitleX.value + horizontalExtension;
});
const subtitleOuterY = computed(() => {
    const verticalExtension = previewWindowHeight.value * 0.5;
    return subtitleY.value + verticalExtension;
});

/**
 * 🎭 数字人角色边框容器样式计算
 * 核心功能：
 * 1. 边框显示控制：默认透明，选中时显示绿色边框
 * 2. 位置跟随：实时跟随图片位置变化  
 * 3. 尺寸同步：与图片尺寸保持一致
 * 4. 层级管理：确保边框在合适的层级显示
 * 5. 交互反馈：提供选中状态的视觉提示
 */
const characterDisplayStyle = computed(() => {
    // 🎨 边框显示逻辑：默认隐藏，选中时显示绿色边框
    let borderColor = 'transparent';  // 默认透明边框
    let borderWidth = '0px';          // 默认无边框
    let boxShadow = 'none';           // 默认无阴影

    if (isCharacterActive.value) {
        // 选中状态：显示绿色边框和阴影效果
        borderColor = '#4CAF50';      // 绿色边框
        borderWidth = '2px';          // 2px边框宽度  
        boxShadow = '0 0 0 1px rgba(76, 175, 80, 0.3)'; // 外发光效果
    }

    return {
        position: 'absolute',
        // 📍 位置计算：使用响应式计算属性
        left: `${characterX.value}px`,
        top: `${characterY.value}px`,
        // 📏 尺寸同步：与图片尺寸一致
        width: `${characterWidth.value}px`,
        height: `${characterHeight.value}px`,
        // 🎯 交互样式
        cursor: 'pointer',
        zIndex: 10,                   // 确保在内容层之上
        border: `${borderWidth} solid ${borderColor}`,
        borderRadius: '12px',         // 圆角效果
        boxSizing: 'border-box',
        backgroundColor: 'transparent', // 透明背景，只显示边框
        boxShadow: boxShadow,
        // 🔄 平滑过渡：边框粗细和阴影的过渡动画
        transition: 'border-width 0.2s ease, box-shadow 0.2s ease'
    };
});

/**
 * 数字人角色图片内容样式（在裁剪层内，会被裁剪）
 * 功能：仅负责图片内容的显示定位
 * 特点：
 * - 位置与边框容器同步
 * - 不响应鼠标事件（由边框容器处理）
 * - 在裁剪层内，超出预览区域的部分会被隐藏
 */
const characterImageContentStyle = computed(() => ({
    position: 'absolute',
    left: `${characterX.value}px`,
    top: `${characterY.value}px`,
    width: `${characterWidth.value}px`,
    height: `${characterHeight.value}px`,
    zIndex: 5, // 在边框之下
    pointerEvents: 'none' // 不响应鼠标事件，交由边框容器处理
}));

// ========================================
// 🎨 背景模块样式计算
// ========================================

/**
 * 背景模块位置和尺寸计算
 * 功能：使用统一的坐标精度处理，确保与数字人层坐标一致
 */
const backgroundModuleX = computed(() => {
    const initialPos = getInitialBackgroundModulePosition();
    const finalX = initialPos.x + userBackgroundModuleOffsetX.value;
    return normalizeCoordinate(finalX);
});

const backgroundModuleY = computed(() => {
    const initialPos = getInitialBackgroundModulePosition();
    const finalY = initialPos.y + userBackgroundModuleOffsetY.value;
    return normalizeCoordinate(finalY);
});

const backgroundModuleWidth = computed(() => {
    const initialSize = getInitialBackgroundModuleSize();
    const finalWidth = initialSize.width * userBackgroundModuleScaleX.value;
    return normalizeCoordinate(finalWidth);
});

const backgroundModuleHeight = computed(() => {
    const initialSize = getInitialBackgroundModuleSize();
    const finalHeight = initialSize.height * userBackgroundModuleScaleY.value;
    return normalizeCoordinate(finalHeight);
});

/**
 * 检查背景配置是否有效
 * 用于决定是否显示背景模块的边框和圆角样式
 */
const hasValidBackgroundConfig = computed(() => {
    const config = props.backgroundConfig;
    if (!config || !config.type) return false;

    // 检查配置值是否有效（非空字符串、null或undefined）
    const hasValidValue = config.value &&
        config.value !== '' &&
        config.value !== null &&
        config.value !== undefined;

    return hasValidValue && (config.type === 'color' || config.type === 'image');
});

/**
 * 背景模块边框容器样式计算
 * 功能：可拖动的背景模块边框和控制点
 * 特点：
 * - 层级在背景层之上，数字人角色之下（z-index: 8）
 * - 支持独立的选中状态管理
 * - 响应式边框和阴影效果
 * - 根据背景配置有效性决定是否显示边框和圆角
 */
const backgroundModuleDisplayStyle = computed(() => {
    // 🎨 根据背景配置有效性和状态设置边框样式
    let borderColor = 'transparent';
    let borderWidth = '2px';
    let boxShadow = 'none';
    let borderRadius = '0px'; // 默认无圆角

    // 只有在背景配置有效且为图案背景时才显示边框和圆角，纯色背景不显示边框
    if (hasValidBackgroundConfig.value && props.backgroundConfig?.type === 'image') {
        borderRadius = '12px'; // 有效配置时显示圆角

        if (isBackgroundModuleActive.value) {
            borderColor = '#4CAF50';      // 绿色边框表示选中
            borderWidth = '2px';
            boxShadow = '0 0 0 1px rgba(76, 175, 80, 0.3)';
        } else if (isBackgroundModuleHovering.value) {
            borderColor = 'rgba(76, 175, 80, 0.6)'; // 半透明绿色边框表示悬停
            borderWidth = '2px';
            boxShadow = '0 0 0 1px rgba(76, 175, 80, 0.3)';
        }
    }

    return {
        position: 'absolute',
        left: `${backgroundModuleX.value}px`,
        top: `${backgroundModuleY.value}px`,
        width: `${backgroundModuleWidth.value}px`,
        height: `${backgroundModuleHeight.value}px`,
        cursor: 'pointer',
        zIndex: 8, // 在背景层之上，数字人角色之下
        border: `${borderWidth} solid ${borderColor}`,
        borderRadius: borderRadius,
        boxSizing: 'border-box',
        backgroundColor: 'transparent',
        boxShadow: boxShadow,
        transition: 'border-width 0.2s ease, box-shadow 0.2s ease'
    };
});

/**
 * 背景模块内容样式（在裁剪层内，会被裁剪）
 * 功能：显示背景颜色或图片内容
 */
const backgroundModuleContentStyle = computed(() => ({
    position: 'absolute',
    left: `${backgroundModuleX.value}px`,
    top: `${backgroundModuleY.value}px`,
    width: `${backgroundModuleWidth.value}px`,
    height: `${backgroundModuleHeight.value}px`,
    zIndex: 3, // 在背景层之上，但在边框之下
    pointerEvents: 'none'
}));

/**
 * 背景模块样式（颜色或图片）
 */
const backgroundModuleStyle = computed(() => {
    if (props.backgroundConfig && props.backgroundConfig.type === 'color') {
        return {
            width: '100%',
            height: '100%',
            backgroundColor: props.backgroundConfig.value,
            borderRadius: '8px'
        };
    } else if (props.backgroundConfig && props.backgroundConfig.type === 'image') {
        return {
            width: '100%',
            height: '100%',
            backgroundImage: `url(${props.backgroundConfig.value})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat',
            borderRadius: '8px'
        };
    }
    return {
        width: '100%',
        height: '100%',
        backgroundColor: '#CCCCCC',
        borderRadius: '8px'
    };
});

/**
 * 🖼️ 第二个图片边框容器样式计算
 * 功能：与数字人角色类似，但层级更高
 * 用途：Logo、装饰元素等辅助图片的显示控制
 * 特点：
 * - 层级高于数字人角色（z-index: 15）
 * - 支持独立的选中状态管理
 * - 相同的边框显示逻辑
 */
const secondImageDisplayStyle = computed(() => {
    let borderColor = 'transparent'; // 默认透明边框
    let borderWidth = '0px';     // 默认无边框
    let boxShadow = 'none';

    if (isSecondImageActive.value) {
        // 当前选中状态：2px绿色边框 + 阴影
        borderColor = '#4CAF50';
        borderWidth = '2px';
        boxShadow = '0 0 0 1px rgba(76, 175, 80, 0.3)';
    }

    return {
        position: 'absolute',
        left: `${secondImageX.value}px`,
        top: `${secondImageY.value}px`,
        width: `${secondImageWidth.value}px`,
        height: `${secondImageHeight.value}px`,
        cursor: 'pointer',
        zIndex: 15, // 确保在第一个图片之上
        border: `${borderWidth} solid ${borderColor}`,
        borderRadius: '12px', // 增加圆角
        boxSizing: 'border-box',
        backgroundColor: 'transparent', // 透明背景，只显示边框
        boxShadow: boxShadow,
        transition: 'border-width 0.2s ease, box-shadow 0.2s ease'
    };
});

/**
 * 第二个图片内容样式（在裁剪层内，会被裁剪）
 * 功能：显示装饰图片的实际内容
 * 层级：高于数字人内容，但低于边框容器
 */
const secondImageContentStyle = computed(() => ({
    position: 'absolute',
    left: `${secondImageX.value}px`,
    top: `${secondImageY.value}px`,
    width: `${secondImageWidth.value}px`,
    height: `${secondImageHeight.value}px`,
    zIndex: 8, // 在第一个图片内容之上，但在边框之下
    pointerEvents: 'none' // 不响应鼠标事件，交由边框容器处理
}));

/**
 * 动态文本布局计算函数
 * 功能：根据容器尺寸计算最佳的文本布局参数
 * @param {number} containerWidth - 容器宽度
 * @param {number} containerHeight - 容器高度
 * @param {number} baseFontSize - 基础字体大小
 * @returns {object} 布局参数对象
 */
const calculateDynamicTextLayout = (containerWidth, containerHeight, baseFontSize) => {
    // 基础参数
    const minLineHeight = 1.2;
    const maxLineHeight = 2.0;
    const basePadding = Math.max(4, baseFontSize * 0.2);

    // 根据容器宽度调整文本布局
    const availableWidth = containerWidth - (basePadding * 2);
    const availableHeight = containerHeight - (basePadding * 2);

    // 估算每行可容纳的字符数（基于字体大小和容器宽度）
    const avgCharWidth = baseFontSize * 0.6; // 中文字符平均宽度约为字体大小的0.6倍
    const maxCharsPerLine = Math.floor(availableWidth / avgCharWidth);

    // 根据容器高度计算最佳行数
    const baseLineHeight = Math.max(1.2, 1.4 - baseFontSize * 0.01);
    const lineHeightPx = baseFontSize * baseLineHeight;
    const maxLines = Math.floor(availableHeight / lineHeightPx);

    // 动态调整行高：容器越高，行高可以适当增加以充分利用空间
    let dynamicLineHeight = baseLineHeight;
    if (maxLines >= 3) {
        // 如果可以显示3行或更多，适当增加行高以改善可读性
        dynamicLineHeight = Math.min(maxLineHeight, baseLineHeight * 1.2);
    } else if (maxLines <= 1) {
        // 如果只能显示1行，减少行高以节省空间
        dynamicLineHeight = Math.max(minLineHeight, baseLineHeight * 0.9);
    }

    // 根据容器宽度调整padding：宽容器可以有更多padding
    let dynamicPadding = basePadding;
    if (containerWidth > 400) {
        dynamicPadding = Math.min(basePadding * 1.5, 20);
    } else if (containerWidth < 200) {
        dynamicPadding = Math.max(basePadding * 0.7, 2);
    }

    // 计算最大高度：基于动态行高和最大行数
    const maxHeight = Math.min(
        availableHeight,
        baseFontSize * dynamicLineHeight * Math.min(maxLines, 3) + dynamicPadding * 2
    );

    return {
        lineHeight: dynamicLineHeight,
        padding: dynamicPadding,
        maxHeight: maxHeight,
        maxLines: Math.min(maxLines, 3),
        maxCharsPerLine: maxCharsPerLine,
        availableWidth: availableWidth,
        availableHeight: availableHeight
    };
};

/**
 * 字幕内容样式计算（在裁剪层内，会被裁剪）
 * 功能：字幕文本内容的显示样式
 * 特点：
 * - 最高显示层级（z-index: 30）
 * - 支持文本样式自定义
 * - 动态响应容器尺寸的文本布局
 * - 自动换行和文字描边效果
 */
const subtitleContentStyle = computed(() => {
    // 计算字幕内容的实际显示位置，确保在裁剪容器内
    const actualLeft = Math.max(0, subtitleX.value);
    const actualTop = Math.max(0, subtitleY.value);

    // 如果字幕超出左边界，调整宽度和内容偏移
    let contentWidth = subtitleWidth.value;
    let contentLeft = actualLeft;

    if (subtitleX.value < 0) {
        // 字幕超出左边界，调整显示区域
        contentWidth = Math.max(0, subtitleWidth.value + subtitleX.value);
        contentLeft = 0;
    }

    // 如果字幕超出右边界，调整宽度
    const previewWidth = previewWindowWidth.value;
    if (actualLeft + contentWidth > previewWidth) {
        contentWidth = Math.max(0, previewWidth - actualLeft);
    }

    // 如果字幕超出上边界，调整高度和内容偏移
    let contentHeight = subtitleHeight.value;
    let contentTop = actualTop;

    if (subtitleY.value < 0) {
        // 字幕超出上边界，调整显示区域
        contentHeight = Math.max(0, subtitleHeight.value + subtitleY.value);
        contentTop = 0;
    }

    // 如果字幕超出下边界，调整高度
    const previewHeight = previewWindowHeight.value;
    if (actualTop + contentHeight > previewHeight) {
        contentHeight = Math.max(0, previewHeight - actualTop);
    }

    // 🎯 新增：计算动态文本布局参数
    const dynamicLayout = calculateDynamicTextLayout(
        contentWidth,
        contentHeight,
        fontSize.value
    );

    return {
        position: 'absolute',
        left: `${contentLeft}px`,
        top: `${contentTop}px`,
        width: `${contentWidth}px`,
        height: `${contentHeight}px`,
        fontSize: `${fontSize.value}px`,
        color: textColor.value,
        fontFamily: fontFamily.value,
        // 🔧 强制样式刷新，确保字体变更能立即生效
        willChange: 'font-family',
        fontWeight: fontWeight.value,
        backgroundColor: 'transparent',
        borderRadius: '4px',
        userSelect: 'none',
        zIndex: 30,
        // 🎯 使用优化的flex布局实现完美的水平和垂直居中
        display: 'flex',
        alignItems: 'center',          // 垂直居中
        justifyContent: 'center',      // 水平居中
        flexDirection: 'column',       // 垂直方向排列，支持多行文本
        // 🎯 动态padding：根据容器尺寸调整，确保在不同尺寸下都能完美居中
        padding: `${dynamicLayout.padding}px`,
        // 确保文本内容不会超出容器边界
        overflow: 'hidden',
        boxSizing: 'border-box',
        // 确保垂直居中生效 - 使用完整高度
        height: '100%',
        // 🔧 修复：动态文字描边效果，支持真正的渐进式粗细变化
        textShadow: borderWidth.value > 0 ? `
            -${borderWidth.value}px -${borderWidth.value}px 0 ${borderColor.value},
            ${borderWidth.value}px -${borderWidth.value}px 0 ${borderColor.value},
            -${borderWidth.value}px ${borderWidth.value}px 0 ${borderColor.value},
            ${borderWidth.value}px ${borderWidth.value}px 0 ${borderColor.value}
        ` : 'none',
        // 文本居中对齐
        textAlign: textAlign.value,    // 使用textAlign变量实现文本居中
        // 🎯 动态换行策略：根据容器宽度优化换行方式
        whiteSpace: contentWidth < 150 ? 'nowrap' : 'normal',  // 窄容器时单行显示，宽容器时允许换行
        // 🎯 动态行高：根据容器尺寸自适应，充分利用垂直空间
        lineHeight: `${dynamicLayout.lineHeight}`,
        // 🎯 智能换行：根据容器宽度调整换行策略
        wordWrap: contentWidth > 200 ? 'break-word' : 'normal',        // 宽容器允许长单词换行
        wordBreak: contentWidth > 300 ? 'keep-all' : 'break-all',      // 超宽容器保持词语完整性
        // 🔧 移除可能影响垂直居中的属性
        // verticalAlign 已删除，避免与flex布局冲突
        fontDisplay: 'swap',           // 优化字体加载显示
        textRendering: 'optimizeLegibility',  // 优化文字渲染
        // 🎯 动态最大高度：基于容器尺寸和动态布局参数计算
        maxHeight: `${dynamicLayout.maxHeight}px`,
        pointerEvents: isPlaying.value ? 'none' : 'auto',
        // 🎯 新增：容器尺寸信息（用于调试，生产环境可移除）
        '--container-width': `${contentWidth}px`,
        '--container-height': `${contentHeight}px`,
        '--max-lines': dynamicLayout.maxLines,
        '--chars-per-line': dynamicLayout.maxCharsPerLine
    };
});

/**
 * 字幕文本span样式计算
 * 功能：为字幕文本span提供动态响应式样式
 * 特点：根据容器尺寸调整文本显示方式
 */
const subtitleTextSpanStyle = computed(() => {
    // 获取当前容器尺寸
    const containerWidth = subtitleWidth.value;
    const containerHeight = subtitleHeight.value;

    // 计算动态布局参数
    const dynamicLayout = calculateDynamicTextLayout(
        containerWidth,
        containerHeight,
        fontSize.value
    );

    return {
        display: 'block',
        textAlign: 'center',
        width: '100%',
        // 🎯 动态继承父容器的布局属性
        lineHeight: 'inherit',
        whiteSpace: 'inherit',
        wordWrap: 'inherit',
        wordBreak: 'inherit',
        // 🎯 新增：根据容器尺寸优化文本显示
        maxWidth: '100%',
        // 🎯 动态文本溢出处理：窄容器时使用省略号，宽容器时正常显示
        overflow: containerWidth < 120 ? 'hidden' : 'visible',
        textOverflow: containerWidth < 120 ? 'ellipsis' : 'clip',
        // 🎯 动态字符间距：根据容器宽度微调
        letterSpacing: containerWidth > 300 ? '0.5px' :
                      containerWidth > 200 ? '0.2px' :
                      containerWidth < 100 ? '-0.2px' : 'normal',
        // 🎯 动态词间距：宽容器时增加词间距提升可读性
        wordSpacing: containerWidth > 400 ? '2px' :
                    containerWidth > 250 ? '1px' : 'normal'
    };
});

/**
 * 字幕边框容器样式计算（在裁剪层外，始终可见）
 * 功能：与背景层和数字人角色层保持一致的边框和控制点
 * 特点：
 * - 层级在内容层之上，用于交互控制
 * - 支持独立的选中状态管理
 * - 响应式边框效果，无阴影
 */
const subtitleDisplayStyle = computed(() => {
    // 🎨 根据状态设置边框样式
    let borderColor = 'transparent';
    let borderWidth = '2px';

    if (isSubtitleActive.value) {
        borderColor = '#4CAF50';      // 绿色边框表示选中
        borderWidth = '2px';
    } else if (isSubtitleHovering.value) {
        borderColor = 'rgba(76, 175, 80, 0.6)'; // 半透明绿色边框表示悬停
        borderWidth = '2px';
    }

    return {
        position: 'absolute',
        left: `${subtitleX.value}px`,
        top: `${subtitleY.value}px`,
        width: `${subtitleWidth.value}px`,
        height: `${subtitleHeight.value}px`,
        zIndex: 31, // 在内容层之上
        border: `${borderWidth} solid ${borderColor}`,
        borderRadius: '12px',
        boxSizing: 'border-box',
        backgroundColor: 'transparent',
        cursor: 'move',
        transition: 'border-width 0.2s ease'
    };
});

// ========================================
// 🎵 时间轴和媒体控制
// ========================================
/**
 * 播放按钮图标计算
 * 功能：根据播放状态动态切换按钮图标
 * 逻辑：播放中显示暂停图标，暂停时显示播放图标
 */
const playButtonImage = computed(() => {
    return isPlaying.value ? bofang2 : bofang1;
});

/**
 * 活动文本内容计算
 * 功能：根据当前时间从时间轴事件中获取应显示的文本
 * 条件：文本事件在当前时间范围内且不等于字幕文本
 * 用途：时间轴控制的文本显示，与字幕系统独立
 */
const activeText = computed(() => {
    const event = timelineEvents.value.find(
        (e) => e.type === 'SHOW_TEXT' && currentTime.value >= e.startTime && currentTime.value <= e.startTime + e.duration
    );
    return event ? event.payload.text : '';
});

// 移除原来的背景样式计算，改为直接在模板中根据backgroundConfig显示对应的元素

/**
 * 播放按钮容器位置计算
 * 功能：根据宽高比调整播放按钮的位置
 * 策略：16:9比例时增加顶部边距，其他比例使用默认样式
 */
const controlsStyle = computed(() => {
    return props.aspectRatio === '16:9'
        ? { marginTop: '45px' }
        : {};
});

// ========================================
// 🎮 媒体和显示控制方法
// ========================================
/**
 * 播放/暂停切换
 * 功能：调用store的播放控制方法
 */
const togglePlay = () => {
    // 如果正在播放，则切换为暂停，不显示提示
    if (isPlaying.value) {
        store.togglePlay();
        return;
    }

    // 🔧 检查总时长，如果为0则提示先生成音频
    if (store.totalDuration === 0) {
        ElMessage.warning('请先生成音频');
        console.log('⚠️ 总时长为0，无法播放，提示用户先生成音频');
        return;
    }

    // 🎵 立即开始播放，不被提示框阻塞
    store.togglePlay();

    // 🔔 异步显示提示框（如果用户还没有选择"不再提示"）
    const dismissed = localStorage.getItem('lipSyncAlertDismissed');
    if (dismissed !== 'true') {
        // 显示提示框，但不影响播放功能
        showLipSyncAlert.value = true;
        // 🕐 启动5秒倒计时
        startAlertCountdown();
    }
};

/**
 * 音频自动停止处理函数
 * 功能：统一处理各种场景下的音频自动停止逻辑
 * @param {string} eventType - 触发停止的事件类型
 * @param {Event} event - 原始事件对象
 */
const handleAutoStopAudio = (eventType, event) => {
    try {
        // 只有在正在播放时才执行停止逻辑
        if (!isPlaying.value) {
            return;
        }

        console.log(`🛑 检测到${eventType}事件，自动停止音频播放`);

        // 调用store的暂停方法停止播放
        store.pause();

        console.log(`✅ 音频播放已自动停止，原因：${eventType}`);
    } catch (error) {
        console.error('❌ 自动停止音频时发生错误:', error);
    }
};

/**
 * 判断点击是否在播放控制区域外
 * 功能：检查用户点击的位置是否在播放相关的控制区域
 * @param {Event} event - 点击事件对象
 * @returns {boolean} 如果点击在播放区域外返回true
 */
const isClickOutsidePlayArea = (event) => {
    try {
        const target = event.target;

        // 检查是否点击了播放控制按钮
        if (target.closest('.play-control-image') || target.closest('.controls')) {
            return false;
        }

        // 检查是否点击了预览窗口内的交互元素
        if (target.closest('.preview-window')) {
            return false;
        }

        // 检查是否点击了右键菜单
        if (target.closest('.context-menu')) {
            return false;
        }

        // 检查是否点击了提示框
        if (target.closest('.lip-sync-alert')) {
            return false;
        }

        // 其他区域的点击都认为是在播放区域外
        return true;
    } catch (error) {
        console.error('❌ 判断点击区域时发生错误:', error);
        return false;
    }
};

/**
 * 处理页面点击事件，实现点击其他区域自动停止播放
 * 功能：当用户点击播放控制区域外的其他地方时，自动停止音频播放
 * @param {Event} event - 点击事件对象
 */
const handleClickOutsidePlay = (event) => {
    try {
        // 只有在正在播放且点击在播放区域外时才停止
        if (isPlaying.value && isClickOutsidePlayArea(event)) {
            handleAutoStopAudio('页面点击其他区域', event);
        }
    } catch (error) {
        console.error('❌ 处理页面点击事件时发生错误:', error);
    }
};

// 🎵 音频自动停止事件处理器（保存引用以便清理）
const audioAutoStopHandlers = {
    // 窗口失去焦点处理器
    windowBlur: (event) => handleAutoStopAudio('窗口失去焦点', event),

    // 页面可见性变化处理器
    visibilityChange: (event) => {
        if (document.visibilityState === 'hidden') {
            handleAutoStopAudio('页面变为不可见', event);
        }
    },

    // 页面即将卸载处理器
    beforeUnload: (event) => handleAutoStopAudio('页面即将卸载', event)
};

/**
 * 启动提示框倒计时
 * 功能：5秒倒计时后自动关闭提示框并保存设置
 */
const startAlertCountdown = () => {
    // 清除可能存在的旧定时器
    if (alertTimer.value) {
        clearInterval(alertTimer.value);
    }

    // 重置倒计时
    alertCountdown.value = 5;

    // 启动倒计时定时器
    alertTimer.value = setInterval(() => {
        alertCountdown.value--;

        if (alertCountdown.value <= 0) {
            // 倒计时结束，自动关闭
            autoCloseLipSyncAlert();
        }
    }, 1000);
};

/**
 * 自动关闭提示框
 * 功能：倒计时结束时自动关闭并保存设置
 */
const autoCloseLipSyncAlert = () => {
    // 清除定时器
    if (alertTimer.value) {
        clearInterval(alertTimer.value);
        alertTimer.value = null;
    }

    // 🔔 保存"不再提示"设置（自动关闭也视为用户已知晓）
    localStorage.setItem('lipSyncAlertDismissed', 'true');
    // 🚫 关闭提示框
    showLipSyncAlert.value = false;

    console.log('⏰ 提示框倒计时结束，自动关闭并保存设置');
};

/**
 * 手动关闭提示框
 * 功能：用户点击"不再提示"按钮时调用
 */
const dismissLipSyncAlert = () => {
    // 清除定时器
    if (alertTimer.value) {
        clearInterval(alertTimer.value);
        alertTimer.value = null;
    }

    // 🔔 保存用户的"不再提示"选择
    localStorage.setItem('lipSyncAlertDismissed', 'true');
    // 🚫 关闭提示框
    showLipSyncAlert.value = false;

    console.log('✅ 用户手动选择不再提示口型对齐警告，提示框已关闭');
};







// ========================================
// 🕰️ 悬停自动选中机制
// ========================================
/**
 * 数字人角色鼠标进入处理
 * 功能：实现悬停1秒自动选中的用户体验优化
 * 逻辑：
 * 1. 已选中状态跳过处理
 * 2. 设置悬停状态标记
 * 3. 启动1秒延时自动选中
 * 4. 清除之前的定时器避免重复触发
 */
const onCharacterMouseEnter = () => {
    if (isCharacterActive.value) return; // 已选中的跳过

    isCharacterHovering.value = true;

    // 🧹 清除之前的定时器，避免重复触发
    if (characterHoverTimer.value) {
        clearTimeout(characterHoverTimer.value);
    }

    // ⏱️ 设置1秒延迟自动选中
    characterHoverTimer.value = setTimeout(() => {
        if (isCharacterHovering.value) {
            isCharacterActive.value = true;
            isSecondImageActive.value = false; // 取消其他选中
            console.log('数字人角色悬停1秒自动选中');
        }
    }, 1000);
};

/**
 * 数字人角色鼠标离开处理
 * 功能：清理悬停状态，取消自动选中定时器
 */
const onCharacterMouseLeave = () => {
    isCharacterHovering.value = false;

    // 🛑 离开时清除定时器，取消自动选中
    if (characterHoverTimer.value) {
        clearTimeout(characterHoverTimer.value);
        characterHoverTimer.value = null;
    }
};

/**
 * 第二个图片鼠标悬停进入处理
 * 功能：与数字人角色相同的悬停自动选中机制
 */
const onSecondImageMouseEnter = () => {
    if (isSecondImageActive.value) return; // 已经选中的不需要悬停选中

    isSecondImageHovering.value = true;

    // 清除之前的定时器
    if (secondImageHoverTimer.value) {
        clearTimeout(secondImageHoverTimer.value);
    }

    // 设置1秒后自动选中
    secondImageHoverTimer.value = setTimeout(() => {
        if (isSecondImageHovering.value) {
            isSecondImageActive.value = true;
            isCharacterActive.value = false;
            isSubtitleActive.value = false;
            console.log('第二个图片悬停1秒自动选中');
        }
    }, 1000);
};

/**
 * 第二个图片鼠标离开处理
 */
const onSecondImageMouseLeave = () => {
    isSecondImageHovering.value = false;

    // 清除定时器，取消自动选中
    if (secondImageHoverTimer.value) {
        clearTimeout(secondImageHoverTimer.value);
        secondImageHoverTimer.value = null;
    }
};

/**
 * 字幕鼠标悬停进入处理
 */
const onSubtitleMouseEnter = () => {
    if (isSubtitleActive.value) return; // 已经选中的不需要悬停选中

    isSubtitleHovering.value = true;

    // 清除之前的定时器
    if (subtitleHoverTimer.value) {
        clearTimeout(subtitleHoverTimer.value);
    }

    // 设置1秒后自动选中
    subtitleHoverTimer.value = setTimeout(() => {
        if (isSubtitleHovering.value) {
            isSubtitleActive.value = true;
            isCharacterActive.value = false;
            isSecondImageActive.value = false;
            console.log('字幕悬停1秒自动选中');
        }
    }, 1000);
};

/**
 * 字幕鼠标离开处理
 */
const onSubtitleMouseLeave = () => {
    isSubtitleHovering.value = false;

    // 清除定时器，取消自动选中
    if (subtitleHoverTimer.value) {
        clearTimeout(subtitleHoverTimer.value);
        subtitleHoverTimer.value = null;
    }
};

/**
 * 字幕点击选中处理
 * 功能：点击字幕时立即选中，取消其他元素选中状态
 * 防冒泡：阻止事件向上传播到预览窗口
 */
const onSubtitleClick = (event) => {
    event.stopPropagation(); // 阻止事件冒泡
    isSubtitleActive.value = true;
    isCharacterActive.value = false;
    isSecondImageActive.value = false;
};

// ========================================
// 🖱️ 智能点击选择系统
// ========================================


/**
 * 预览窗口点击选中处理
 * 核心功能：实现智能的多元素点击选择系统
 * 
 * 🧠 智能选中逻辑：
 * 1. 边界检测：判断点击位置是否在图片边界内
 * 2. 重叠处理：重叠区域优先选择层级更高的图片  
 * 3. 状态切换：更新选中状态，取消其他图片选中
 * 4. 防误触：拖拽和拉伸时不响应点击
 * 
 * 🎯 优先级顺序：字幕 > 装饰图片 > 数字人角色 > 空白区域
 * 
 * 📍 坐标系统：所有计算基于预览窗口的相对坐标
 */
const onPreviewWindowClick = (event) => {
    console.log('🖱️ 预览窗口被点击');

    // 🖱️ 优先处理：隐藏右键菜单（如果可见）
    if (contextMenu.value.visible) {
        hideContextMenu();
        console.log('🖱️ 预览窗口点击：隐藏右键菜单');
    }

    // 🚫 防误触：拖拽或拉伸进行中时不处理点击
    // 注意：由于拖拽状态现在由各个处理器内部管理，这里暂时移除拖拽检测
    // 拖拽过程中会阻止事件冒泡，所以正常情况下不会触发点击事件
    if (isResizingCharacter || isResizingSecondImage || isResizingSubtitle) {
        console.log('🚫 拉伸进行中，忽略点击');
        return;
    }

    // 📍 获取点击位置相对于预览窗口的坐标
    const rect = event.currentTarget.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const clickY = event.clientY - rect.top;
    console.log(`📍 点击坐标: (${clickX}, ${clickY})`);

    // 🔍 边界检测算法：检查点击位置是否在各元素边界内
    const isInCharacterBounds = showCharacter.value && (
        clickX >= characterX.value &&
        clickX <= characterX.value + characterWidth.value &&
        clickY >= characterY.value &&
        clickY <= characterY.value + characterHeight.value
    );

    const isInSecondImageBounds = showSecondImage.value && (
        clickX >= secondImageX.value &&
        clickX <= secondImageX.value + secondImageWidth.value &&
        clickY >= secondImageY.value &&
        clickY <= secondImageY.value + secondImageHeight.value
    );

    const isInBackgroundModuleBounds = showBackgroundModule.value && (
        clickX >= backgroundModuleX.value &&
        clickX <= backgroundModuleX.value + backgroundModuleWidth.value &&
        clickY >= backgroundModuleY.value &&
        clickY <= backgroundModuleY.value + backgroundModuleHeight.value
    );

    const isInSubtitleBounds = showSubtitle.value && (
        clickX >= subtitleX.value &&
        clickX <= subtitleX.value + subtitleWidth.value &&
        clickY >= subtitleY.value &&
        clickY <= subtitleY.value + subtitleHeight.value
    );

    // 🧠 智能选择逻辑：处理重叠和优先级（字幕 > 装饰图片 > 数字人角色 > 背景模块）
    if (isInSubtitleBounds) {
        // 字幕区域：最高优先级
        isSubtitleActive.value = true;
        isSecondImageActive.value = false;
        isCharacterActive.value = false;
        isBackgroundModuleActive.value = false;
    } else if (isInSecondImageBounds && isInCharacterBounds) {
        // 重叠区域：选择层级更高的装饰图片
        isSecondImageActive.value = true;
        isCharacterActive.value = false;
        isSubtitleActive.value = false;
        isBackgroundModuleActive.value = false;
    } else if (isInSecondImageBounds) {
        // 仅装饰图片区域
        isSecondImageActive.value = true;
        isCharacterActive.value = false;
        isSubtitleActive.value = false;
        isBackgroundModuleActive.value = false;
    } else if (isInCharacterBounds) {
        // 仅数字人角色区域
        isCharacterActive.value = true;
        isSecondImageActive.value = false;
        isSubtitleActive.value = false;
        isBackgroundModuleActive.value = false;
    } else if (isInBackgroundModuleBounds) {
        // 🎯 背景模块区域 - 仅图案背景可以被选中，纯色背景不可选中
        if (props.backgroundConfig && props.backgroundConfig.type === 'image') {
            isBackgroundModuleActive.value = true;
            isCharacterActive.value = false;
            isSecondImageActive.value = false;
            isSubtitleActive.value = false;
            console.log('🖼️ 图案背景被选中');
        } else {
            // 纯色背景不可选中，取消所有选择
            isCharacterActive.value = false;
            isSecondImageActive.value = false;
            isSubtitleActive.value = false;
            isBackgroundModuleActive.value = false;
            console.log('🚫 纯色背景不可选中');
        }
    } else {
        // 空白区域：取消所有选择
        isCharacterActive.value = false;
        isSecondImageActive.value = false;
        isSubtitleActive.value = false;
        isBackgroundModuleActive.value = false;
    }
};

// ========================================
// 🚀 统一拖拽系统 - 通用拖拽处理模板
// ========================================

/**
 * 统一的拖拽处理模板
 * 所有图层的拖拽逻辑都基于此模板，确保一致性
 * @param {Object} config - 拖拽配置对象
 * @param {string} config.elementType - 元素类型 ('character', 'secondImage', 'subtitle', 'backgroundModule')
 * @param {Object} config.activeState - 选中状态的ref对象
 * @param {Object} config.offsetX - X轴偏移量的ref对象
 * @param {Object} config.offsetY - Y轴偏移量的ref对象
 * @param {Function} config.getSizeFunction - 获取当前尺寸的函数
 * @param {Function} config.getInitialPositionFunction - 获取初始位置的函数
 * @param {Function} config.clearHoverFunction - 清理悬停状态的函数
 * @param {Array} config.otherActiveStates - 其他需要清除的选中状态
 * @param {Object} config.hoverTimer - 悬停定时器ref对象
 * @param {Object} config.hoverState - 悬停状态ref对象
 * @param {boolean} config.skipCondition - 跳过拖拽的条件（如纯色背景）
 * @returns {Object} 返回拖拽处理函数集合
 */
const createUnifiedDragHandlers = (config) => {
    const {
        elementType,
        activeState,
        offsetX,
        offsetY,
        getSizeFunction,
        getInitialPositionFunction,
        clearHoverFunction,
        otherActiveStates = [],
        hoverTimer,
        hoverState,
        skipCondition = false
    } = config;

    // 拖拽状态变量
    let isDragging = false;
    let dragStartX = 0;
    let dragStartY = 0;
    let initialOffsetX = 0;
    let initialOffsetY = 0;

    /**
     * 开始拖拽 - 统一处理逻辑
     */
    const startDrag = (event) => {
        // 检查跳过条件
        if (skipCondition) {
            return;
        }

        // 自动选中：未选中时先选中再拖拽
        if (!activeState.value) {
            activeState.value = true;
            // 清除其他元素的选中状态
            otherActiveStates.forEach(state => {
                if (state && state.value !== undefined) {
                    state.value = false;
                }
            });

            // 清理悬停状态
            if (clearHoverFunction) {
                clearHoverFunction();
            }
        }

        event.preventDefault();
        event.stopPropagation();

        // 拖拽初始化
        isDragging = true;
        dragStartX = event.clientX;
        dragStartY = event.clientY;

        // 记录拖拽开始时的用户偏移量
        initialOffsetX = offsetX.value;
        initialOffsetY = offsetY.value;

        // 🔍 调试：检查拖拽开始时的状态
        if (elementType === 'character') {
            console.log('🖱️ 数字人拖拽开始:', {
                元素类型: elementType,
                拖拽开始位置: { x: dragStartX, y: dragStartY },
                初始偏移量: { x: initialOffsetX, y: initialOffsetY },
                当前偏移量: { x: offsetX.value, y: offsetY.value },
                时间戳: Date.now()
            });
        }

        // 绑定全局事件
        document.addEventListener('mousemove', onDrag);
        document.addEventListener('mouseup', stopDrag);
    };

    /**
         * 拖拽过程处理 - 统一处理逻辑
     */
    const onDrag = (event) => {
        if (!isDragging) return;

        // 计算鼠标相对移动距离
        const deltaX = event.clientX - dragStartX;
        const deltaY = event.clientY - dragStartY;

        // 基于初始偏移量计算新的偏移量
        let newOffsetX = initialOffsetX + deltaX;
        let newOffsetY = initialOffsetY + deltaY;

        // 边界限制：使用统一的边界配置
        const borderSize = BOUNDARY_CONFIG.borderSize;
        const currentSize = getSizeFunction();
        const currentWidth = currentSize.width;
        const currentHeight = currentSize.height;
        const initialPos = getInitialPositionFunction();

        const minOffsetX = -(currentWidth - borderSize) - initialPos.x;
        const maxOffsetX = previewWindowWidth.value - borderSize - initialPos.x;
        const minOffsetY = -(currentHeight - borderSize) - initialPos.y;
        const maxOffsetY = previewWindowHeight.value - borderSize - initialPos.y;

        // 应用边界限制
        newOffsetX = Math.max(minOffsetX, Math.min(maxOffsetX, newOffsetX));
        newOffsetY = Math.max(minOffsetY, Math.min(maxOffsetY, newOffsetY));

        // 直接更新页面坐标偏移量
        offsetX.value = newOffsetX;
        offsetY.value = newOffsetY;

        // 🔍 调试：追踪拖拽过程
        if (elementType === 'character') {
            console.log('🖱️ 数字人拖拽过程:', {
                元素类型: elementType,
                鼠标移动: { deltaX, deltaY },
                初始偏移: { x: initialOffsetX, y: initialOffsetY },
                新偏移: { x: newOffsetX, y: newOffsetY },
                边界限制: { minX: minOffsetX, maxX: maxOffsetX, minY: minOffsetY, maxY: maxOffsetY }
            });
        }
    };

    /**
         * 停止拖拽 - 统一处理逻辑
     */
    const stopDrag = () => {
        if (!isDragging) return;

        isDragging = false;

        // 解绑全局事件，防止内存泄漏
        document.removeEventListener('mousemove', onDrag);
        document.removeEventListener('mouseup', stopDrag);

        // 发射位置更新事件和特定元素移动事件
        emitPositionUpdate();

        // 发射特定元素移动事件
        if (elementType === 'character') {
            emit('characterMoved', getElementPosition('character'));
        } else if (elementType === 'secondImage') {
            emit('secondImageMoved', getElementPosition('secondImage'));
        } else if (elementType === 'subtitle') {
            emit('subtitleMoved', getElementPosition('subtitle'));
        }
    };

    return {
        startDrag,
        onDrag,
        stopDrag
    };
};

// ========================================
// 🚀 统一拖拽系统 - 数字人角色
// ========================================

/**
 * 数字人角色拖拽处理器
 * 基于统一拖拽模板，确保与字幕图层逻辑一致
 */
const characterDragHandlers = createUnifiedDragHandlers({
    elementType: 'character',
    activeState: isCharacterActive,
    offsetX: userCharacterOffsetX,
    offsetY: userCharacterOffsetY,
    getSizeFunction: () => ({ width: characterWidth.value, height: characterHeight.value }),
    getInitialPositionFunction: getInitialCharacterPosition,
    clearHoverFunction: () => {
        if (characterHoverTimer.value) {
            clearTimeout(characterHoverTimer.value);
            characterHoverTimer.value = null;
        }
        isCharacterHovering.value = false;
    },
    otherActiveStates: [isSecondImageActive, isSubtitleActive, isBackgroundModuleActive],
    hoverTimer: characterHoverTimer,
    hoverState: isCharacterHovering
});

/**
 * 数字人角色鼠标按下事件处理
 */
const handleCharacterMouseDown = (event) => {
    if (!isPlaying.value) {
        startCharacterImageDrag(event);
    }
};

/**
 * 开始拖拽数字人角色 - 统一架构
 */
const startCharacterImageDrag = characterDragHandlers.startDrag;

/**
 * 拖拽过程处理 - 数字人角色统一架构
 */
const onCharacterImageDrag = characterDragHandlers.onDrag;

/**
 * 停止拖拽数字人角色 - 统一架构
 */
const stopCharacterImageDrag = characterDragHandlers.stopDrag;

// ========================================
// 🚀 统一拖拽系统 - 装饰图片
// ========================================

/**
 * 装饰图片拖拽处理器
 * 基于统一拖拽模板，确保与字幕图层逻辑一致
 */
const secondImageDragHandlers = createUnifiedDragHandlers({
    elementType: 'secondImage',
    activeState: isSecondImageActive,
    offsetX: userCharacterOffsetX,    // 🎯 修复：使用统一的数字人数据
    offsetY: userCharacterOffsetY,    // 🎯 修复：使用统一的数字人数据
    getSizeFunction: () => ({ width: secondImageWidth.value, height: secondImageHeight.value }),
    getInitialPositionFunction: getInitialSecondImagePosition,
    clearHoverFunction: () => {
        if (secondImageHoverTimer.value) {
            clearTimeout(secondImageHoverTimer.value);
            secondImageHoverTimer.value = null;
        }
        isSecondImageHovering.value = false;
    },
    otherActiveStates: [isCharacterActive, isSubtitleActive, isBackgroundModuleActive],
    hoverTimer: secondImageHoverTimer,
    hoverState: isSecondImageHovering
});

/**
 * 第二层数字人鼠标按下事件处理
 */
const handleSecondImageMouseDown = (event) => {
    if (!isPlaying.value) {
        startSecondImageDrag(event);
    }
};

/**
 * 开始拖拽装饰图片 - 统一架构
 */
const startSecondImageDrag = secondImageDragHandlers.startDrag;

/**
 * 拖拽过程处理 - 装饰图片统一架构
 */
const onSecondImageDrag = secondImageDragHandlers.onDrag;

/**
 * 停止拖拽装饰图片 - 统一架构
 */
const stopSecondImageDrag = secondImageDragHandlers.stopDrag;

// ========================================
// 🚀 统一拖拽系统 - 背景模块
// ========================================

/**
 * 背景模块拖拽处理器
 * 基于统一拖拽模板，确保与字幕图层逻辑一致
 */
const backgroundModuleDragHandlers = createUnifiedDragHandlers({
    elementType: 'backgroundModule',
    activeState: isBackgroundModuleActive,
    offsetX: userBackgroundModuleOffsetX,
    offsetY: userBackgroundModuleOffsetY,
    getSizeFunction: () => ({ width: backgroundModuleWidth.value, height: backgroundModuleHeight.value }),
    getInitialPositionFunction: getInitialBackgroundModulePosition,
    clearHoverFunction: () => {
        if (backgroundModuleHoverTimer.value) {
            clearTimeout(backgroundModuleHoverTimer.value);
            backgroundModuleHoverTimer.value = null;
        }
        isBackgroundModuleHovering.value = false;
    },
    otherActiveStates: [isCharacterActive, isSecondImageActive, isSubtitleActive],
    hoverTimer: backgroundModuleHoverTimer,
    hoverState: isBackgroundModuleHovering,
    // 🔧 移除静态skipCondition，改为动态检查
    skipCondition: false
});

/**
 * 开始拖拽背景模块 - 统一架构
 * 🎯 添加动态检查：纯色背景禁止拖拽
 */
const startBackgroundModuleDrag = (event) => {
    // 🚫 动态检查：纯色背景禁止拖拽
    if (props.backgroundConfig && props.backgroundConfig.type === 'color') {
        console.log('🚫 纯色背景禁止拖拽');
        return;
    }
    
    // 调用统一拖拽处理器
    backgroundModuleDragHandlers.startDrag(event);
};

/**
 * 拖拽过程处理 - 背景模块统一架构
 */
const onBackgroundModuleDrag = backgroundModuleDragHandlers.onDrag;

/**
 * 停止拖拽背景模块 - 统一架构
 */
const stopBackgroundModuleDrag = backgroundModuleDragHandlers.stopDrag;

// ========================================
// 🎨 背景模块悬停事件处理
// ========================================

// 背景模块鼠标进入事件
const onBackgroundModuleMouseEnter = () => {
    // 🚫 纯色背景禁用悬停效果
    if (props.backgroundConfig && props.backgroundConfig.type === 'color') {
        return;
    }

    if (backgroundModuleHoverTimer.value) {
        clearTimeout(backgroundModuleHoverTimer.value);
        backgroundModuleHoverTimer.value = null;
    }

    backgroundModuleHoverTimer.value = setTimeout(() => {
        isBackgroundModuleHovering.value = true;
    }, 100);
};

// 背景模块鼠标离开事件
const onBackgroundModuleMouseLeave = () => {
    // 🚫 纯色背景禁用悬停效果
    if (props.backgroundConfig && props.backgroundConfig.type === 'color') {
        return;
    }

    if (backgroundModuleHoverTimer.value) {
        clearTimeout(backgroundModuleHoverTimer.value);
        backgroundModuleHoverTimer.value = null;
    }
    isBackgroundModuleHovering.value = false;
};

// ========================================
// 🚀 统一拖拽系统 - 字幕（参考标准）
// ========================================

/**
 * 字幕拖拽处理器
 * 作为所有图层拖拽逻辑的参考标准
 */
const subtitleDragHandlers = createUnifiedDragHandlers({
    elementType: 'subtitle',
    activeState: isSubtitleActive,
    offsetX: userSubtitleOffsetX,
    offsetY: userSubtitleOffsetY,
    getSizeFunction: () => ({ width: subtitleWidth.value, height: subtitleHeight.value }),
    getInitialPositionFunction: getInitialSubtitlePosition,
    clearHoverFunction: () => {
        if (subtitleHoverTimer.value) {
            clearTimeout(subtitleHoverTimer.value);
            subtitleHoverTimer.value = null;
        }
        isSubtitleHovering.value = false;
    },
    otherActiveStates: [isCharacterActive, isSecondImageActive, isBackgroundModuleActive],
    hoverTimer: subtitleHoverTimer,
    hoverState: isSubtitleHovering
});

/**
 * 开始拖拽字幕 - 统一架构（参考标准）
 */
const startSubtitleDrag = subtitleDragHandlers.startDrag;

/**
 * 拖拽过程处理 - 字幕统一架构（参考标准）
 */
const onSubtitleDrag = subtitleDragHandlers.onDrag;

/**
 * 停止拖拽字幕 - 统一架构（参考标准）
 */
const stopSubtitleDrag = subtitleDragHandlers.stopDrag;

// ========================================
// 🔧 统一拖拽系统调试功能
// ========================================

/**
 * 统一拖拽系统状态调试
 * 用于验证所有图层的拖拽处理器是否正常工作
 */
const debugDragSystem = () => {
    console.log('🔧 统一拖拽系统状态检查:', {
        数字人拖拽处理器: {
            存在: !!characterDragHandlers,
            函数: characterDragHandlers ? Object.keys(characterDragHandlers) : null
        },
        装饰图片拖拽处理器: {
            存在: !!secondImageDragHandlers,
            函数: secondImageDragHandlers ? Object.keys(secondImageDragHandlers) : null
        },
        背景模块拖拽处理器: {
            存在: !!backgroundModuleDragHandlers,
            函数: backgroundModuleDragHandlers ? Object.keys(backgroundModuleDragHandlers) : null
        },
        字幕拖拽处理器: {
            存在: !!subtitleDragHandlers,
            函数: subtitleDragHandlers ? Object.keys(subtitleDragHandlers) : null
        },
        坐标转换函数: {
            pageToStandardCoord: typeof pageToStandardCoord === 'function',
            standardToPageCoord: typeof standardToPageCoord === 'function'
        }
    });
};

// ========================================
// 🎬 字幕数据管理方法
// ========================================





// ========================================
// 🕐 时间格式化工具
// ========================================

/**
 * 时间格式化函数
 * 功能：将秒数转换为hh:mm:ss格式显示
 * @param {number} seconds - 秒数
 * @returns {string} 格式化后的时间字符串 (如 "00:02:30")
 */
const formatTime = (seconds) => {
    if (!seconds || isNaN(seconds)) return '00:00:00';
    const hours = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

/**
 * 组件挂载时的初始化操作
 */
onMounted(async () => {
    // 重置字幕位置到新的居中位置
    userSubtitleOffsetX.value = 0;
    userSubtitleOffsetY.value = 0;
    userSubtitleScaleX.value = 1;
    userSubtitleScaleY.value = 1;

    // 🧹 强制清除字幕数据，避免显示旧的测试数据
    if (!props.digitalHumanConfig.url) {
        store.clearSubtitleData();
    }

    // 🎨 初始化字体加载 - 如果已经设置了字体，立即加载
    if (props.subtitleConfig.fontName) {
        try {
            await loadFont(props.subtitleConfig.fontName, props.subtitleConfig.fontUrl);
        } catch (error) {
            // 初始字体加载失败，使用默认字体
        }
    }

    // 🚀 预加载常用字体（后台静默加载，不阻塞界面）
    const commonFonts = ['Microsoft YaHei', 'SimHei'];
    commonFonts.forEach(async (fontName) => {
        try {
            await FontLoader.loadFont(fontName);
        } catch (error) {
            // 预加载失败不影响主要功能
        }
    });
});

// ========================================
// 🔗 暴露给父组件的方法
// ========================================

/**
 * 取消所有元素选中状态的方法
 * 功能：供父组件调用，用于处理全局点击事件
 * 用途：当用户点击预览区域外时，取消字幕等元素的选中状态
 */
const clearAllSelections = () => {
    // 清除所有激活状态
    isCharacterActive.value = false;
    isSecondImageActive.value = false;
    isSubtitleActive.value = false;
    isBackgroundModuleActive.value = false;

    // 🎯 同时清除所有悬停状态，确保边框和拖拽点完全隐藏
    isCharacterHovering.value = false;
    isSecondImageHovering.value = false;
    isSubtitleHovering.value = false;
    isBackgroundModuleHovering.value = false;

    console.log('🧹 已清除所有元素的选中状态和悬停状态');
};



const showLipSyncAlert = ref(false);
const alertCountdown = ref(5); // 倒计时秒数
const alertTimer = ref(null); // 定时器引用

// ========================================
// 🖱️ 右键菜单状态管理
// ========================================

/**
 * 右键菜单状态对象
 * 包含显示状态、位置坐标和目标元素类型
 */
const contextMenu = ref({
    visible: false,    // 菜单显示状态
    x: 0,             // 菜单X坐标
    y: 0,             // 菜单Y坐标
    targetElement: '' // 目标元素类型: 'character' | 'secondImage' | 'subtitle'
});

/**
 * 隐藏右键菜单
 */
const hideContextMenu = () => {
    if (contextMenu.value.visible) {
        contextMenu.value.visible = false;
        console.log('🖱️ 右键菜单已隐藏');
    }
};

/**
 * 计算右键菜单的定位样式
 * 功能：根据鼠标点击位置动态定位菜单
 */
const contextMenuStyle = computed(() => ({
    position: 'fixed',
    left: `${contextMenu.value.x}px`,
    top: `${contextMenu.value.y}px`,
    zIndex: 1000
}));

/**
 * 显示右键菜单
 * @param {MouseEvent} event - 鼠标右键事件对象
 * @param {string} elementType - 目标元素类型
 */
const showContextMenu = (event, elementType) => {
    // 只在对应元素显示时才显示菜单
    if ((elementType === 'character' && !showCharacter.value) ||
        (elementType === 'secondImage' && !showSecondImage.value) ||
        (elementType === 'subtitle' && !showSubtitle.value)) {
        return;
    }

    // 阻止默认右键菜单
    event.preventDefault();
    event.stopPropagation();

    // 设置菜单位置和目标元素
    contextMenu.value = {
        visible: true,
        x: event.clientX,
        y: event.clientY,
        targetElement: elementType
    };

    console.log(`显示${elementType}的右键菜单`);
};




/**
 * 全局点击处理函数 - 隐藏右键菜单
 * 功能：当用户点击页面任何地方（除了右键菜单内部）时，隐藏右键菜单
 * 注意：此函数优先级高于父组件的全局点击处理，确保右键菜单能正确隐藏
 * @param {MouseEvent} event - 鼠标点击事件对象
 */
const handleGlobalClickForContextMenu = (event) => {
    // 如果右键菜单不可见，无需处理
    if (!contextMenu.value.visible) {
        return;
    }

    // 检查点击目标是否在右键菜单内部
    const contextMenuElement = document.querySelector('.context-menu');
    if (contextMenuElement && contextMenuElement.contains(event.target)) {
        // 点击在右键菜单内部，不隐藏菜单
        // 阻止事件冒泡，避免触发父组件的全局点击处理
        event.stopPropagation();
        return;
    }

    // 点击在右键菜单外部，隐藏菜单
    hideContextMenu();
    console.log('🖱️ 全局点击：隐藏右键菜单');

    // 不阻止事件冒泡，让父组件的全局点击处理继续执行
    // 这样可以同时隐藏右键菜单和清除元素选中状态
};

/**
 * 删除元素功能
 * 根据目标元素类型隐藏对应的元素
 */
const deleteElement = () => {
    const elementType = contextMenu.value.targetElement;

    switch (elementType) {
        case 'character':
            showCharacter.value = false;
            isCharacterActive.value = false;
            console.log('数字人角色已删除');
            break;
        case 'secondImage':
            showSecondImage.value = false;
            isSecondImageActive.value = false;
            console.log('装饰图片已删除');
            // 🎯 发射数字人清空事件，同时清空轨道中的数字人图片
            emit('digital-human-cleared');
            console.log('✅ 已发射数字人清空事件，轨道中的数字人图片将被清空');
            break;
        case 'subtitle':
            showSubtitle.value = false;
            isSubtitleActive.value = false;
            console.log('字幕已删除');
            break;
        case 'backgroundModule':
            showBackgroundModule.value = false;
            isBackgroundModuleActive.value = false;
            // 🎯 发射背景清空事件，同时清空父组件中的背景配置
            emit('background-cleared');
            break;
    }

    // 隐藏菜单
    hideContextMenu();

    // 发射位置更新事件
    emitPositionUpdate();
};

/**
 * 键盘删除功能
 * 根据当前选中的图层执行删除操作
 */
const deleteSelectedElement = (elementType) => {
    switch (elementType) {
        case 'character':
            showCharacter.value = false;
            isCharacterActive.value = false;
            console.log('⌨️ 键盘删除：数字人角色已删除');
            break;
        case 'secondImage':
            showSecondImage.value = false;
            isSecondImageActive.value = false;
            console.log('⌨️ 键盘删除：装饰图片已删除');
            // 🎯 发射数字人清空事件，同时清空轨道中的数字人图片
            emit('digital-human-cleared');
            console.log('✅ 已发射数字人清空事件，轨道中的数字人图片将被清空');
            break;
        case 'subtitle':
            showSubtitle.value = false;
            isSubtitleActive.value = false;
            console.log('⌨️ 键盘删除：字幕已删除');
            break;
        case 'backgroundModule':
            showBackgroundModule.value = false;
            isBackgroundModuleActive.value = false;
            console.log('⌨️ 键盘删除：背景模块已删除');
            // 🎯 发射背景清空事件，同时清空父组件中的背景配置
            emit('background-cleared');
            break;
    }

    // 发射位置更新事件
    emitPositionUpdate();
};

/**
 * 键盘事件处理函数
 * 监听 Delete 和 Backspace 键，删除当前选中的图层
 * @param {KeyboardEvent} event - 键盘事件对象
 */
const handleKeyboardDelete = (event) => {
    // 只处理 Delete 和 Backspace 键
    if (event.key !== 'Delete' && event.key !== 'Backspace') {
        return;
    }

    // 播放状态下禁用键盘删除
    if (isPlaying.value) {
        console.log('⌨️ 播放状态下禁用键盘删除');
        return;
    }

    // 防止在输入框等可编辑元素中误触发删除
    const activeElement = document.activeElement;
    if (activeElement && (
        activeElement.tagName === 'INPUT' ||
        activeElement.tagName === 'TEXTAREA' ||
        activeElement.contentEditable === 'true'
    )) {
        console.log('⌨️ 在输入框中，跳过键盘删除');
        return;
    }

    // 按优先级检查当前选中的图层（字幕 → 数字人 → 装饰图片 → 背景）
    let targetElement = null;

    if (isSubtitleActive.value && showSubtitle.value) {
        targetElement = 'subtitle';
    } else if (isCharacterActive.value && showCharacter.value) {
        targetElement = 'character';
    } else if (isSecondImageActive.value && showSecondImage.value) {
        targetElement = 'secondImage';
    } else if (isBackgroundModuleActive.value && showBackgroundModule.value) {
        targetElement = 'backgroundModule';
    }

    // 如果有选中的图层，执行删除操作
    if (targetElement) {
        // 阻止默认行为和事件冒泡
        event.preventDefault();
        event.stopPropagation();

        console.log(`⌨️ 键盘删除：准备删除 ${targetElement}`);
        deleteSelectedElement(targetElement);
    } else {
        console.log('⌨️ 没有选中的图层，跳过键盘删除');
    }
};

/**
 * 处理删除按钮点击事件
 * 功能：确保删除操作正确执行并隐藏右键菜单
 * @param {MouseEvent} event - 鼠标点击事件对象
 */
const handleDeleteClick = (event) => {
    // 阻止事件冒泡，避免触发全局点击监听器
    event.stopPropagation();

    // 执行删除操作
    deleteElement();

    console.log('🗑️ 删除按钮点击：执行删除操作并隐藏菜单');
};

// 监听全局点击事件来隐藏右键菜单
onMounted(() => {
    console.log('PreviewEditor组件已挂载');

    // 添加全局点击监听器来隐藏右键菜单
    // 使用优化后的处理函数，确保点击菜单内部不会隐藏菜单
    document.addEventListener('click', handleGlobalClickForContextMenu);
    document.addEventListener('contextmenu', handleGlobalClickForContextMenu);

    // ⌨️ 添加键盘删除事件监听器
    document.addEventListener('keydown', handleKeyboardDelete);

    // 🎵 添加音频自动停止相关的事件监听器
    // 监听窗口失去焦点事件（用户切换到其他应用程序）
    window.addEventListener('blur', audioAutoStopHandlers.windowBlur);

    // 监听页面可见性变化事件（用户切换标签页、最小化浏览器等）
    document.addEventListener('visibilitychange', audioAutoStopHandlers.visibilityChange);

    // 监听页面即将卸载事件（用户刷新页面、关闭页面、路由跳转等）
    window.addEventListener('beforeunload', audioAutoStopHandlers.beforeUnload);

    // 监听页面点击事件（用户点击播放区域外的其他地方）
    document.addEventListener('click', handleClickOutsidePlay);

    // 🔧 添加窗口大小变化监听，用于响应式适配
    const handleResize = () => {
        console.log('🔧 窗口大小变化，触发预览窗口尺寸重新计算');
        // 触发预览窗口样式的重新计算（计算属性会自动响应）
        nextTick(() => {
            console.log('📐 预览窗口尺寸已重新计算');
        });
    };
    window.addEventListener('resize', handleResize);
    
    // 将事件处理函数存储到组件实例，供清理时使用
    if (typeof window !== 'undefined') {
        window._previewEditorResizeHandler = handleResize;
    }

    console.log('✅ 全局右键菜单点击监听器已注册');
    console.log('✅ 键盘删除事件监听器已注册');
    console.log('✅ 音频自动停止事件监听器已注册');
    console.log('✅ 窗口大小变化监听器已注册');
});

// 组件卸载时清理事件监听器
onUnmounted(() => {
    // 清理右键菜单相关的事件监听器
    document.removeEventListener('click', handleGlobalClickForContextMenu);
    document.removeEventListener('contextmenu', handleGlobalClickForContextMenu);

    // ⌨️ 清理键盘删除事件监听器
    document.removeEventListener('keydown', handleKeyboardDelete);

    // 🔧 清理窗口大小变化监听器
    if (typeof window !== 'undefined' && window._previewEditorResizeHandler) {
        window.removeEventListener('resize', window._previewEditorResizeHandler);
        delete window._previewEditorResizeHandler;
    }

    // 🕐 清理提示框倒计时定时器
    if (alertTimer.value) {
        clearInterval(alertTimer.value);
        alertTimer.value = null;
        console.log('🧹 组件卸载时清理提示框倒计时定时器');
    }

    // 🎵 清理音频自动停止相关的事件监听器
    // 清理窗口失去焦点监听器
    window.removeEventListener('blur', audioAutoStopHandlers.windowBlur);

    // 清理页面可见性变化监听器
    document.removeEventListener('visibilitychange', audioAutoStopHandlers.visibilityChange);

    // 清理页面即将卸载监听器
    window.removeEventListener('beforeunload', audioAutoStopHandlers.beforeUnload);

    // 清理页面点击监听器
    document.removeEventListener('click', handleClickOutsidePlay);

    console.log('🧹 全局右键菜单点击监听器已清理');
    console.log('🧹 音频自动停止事件监听器已清理');
});

// ========================================
// 🎨 数据回显方法定义
// ========================================

/**
 * 🎨 字幕配置设置方法（用于数据回显）
 * 功能：根据保存的字幕配置数据，恢复字幕的位置和尺寸
 * 支持动态标准坐标系（9:16为1080×1920，16:9为1920×1080）和页面坐标系的自动识别和转换
 * @param {Object} subtitleConfigJson - 字幕配置数据
 * @param {boolean} isStandardCoord - 是否为标准坐标系数据，默认true
 */
const setSubtitleConfig = (subtitleConfigJson, isStandardCoord = true) => {
    try {
        if (!subtitleConfigJson) {
            console.log('💡 没有字幕配置数据，跳过设置');
            return;
        }

        console.log('🎨 开始设置字幕配置:', {
            输入数据: subtitleConfigJson,
            坐标系类型: isStandardCoord ? `标准坐标(${STANDARD_SIZE.value.width}×${STANDARD_SIZE.value.height})` : '页面坐标'
        });

        // 🎯 处理位置配置
        if (subtitleConfigJson.x !== undefined && subtitleConfigJson.y !== undefined) {
            let targetX = parseFloat(subtitleConfigJson.x) || 0;
            let targetY = parseFloat(subtitleConfigJson.y) || 0;

            // 🔄 坐标系转换：如果是标准坐标，转换为页面坐标
            if (isStandardCoord) {
                const pageSize = {
                    width: previewWindowWidth.value,
                    height: previewWindowHeight.value
                };

                const pageCoord = standardToPageCoord({
                    x: targetX,
                    y: targetY,
                    width: 0,  // 位置转换时宽高设为0
                    height: 0
                }, pageSize);

                targetX = pageCoord.x;
                targetY = pageCoord.y;

                console.log('🔄 标准坐标转换为页面坐标:', {
                    标准坐标: { x: subtitleConfigJson.x, y: subtitleConfigJson.y },
                    页面坐标: { x: targetX, y: targetY },
                    页面尺寸: pageSize
                });
            }

            // 计算位置偏移量（相对于初始位置的偏移）
            const initialPosition = getInitialSubtitlePosition();
            userSubtitleOffsetX.value = targetX - initialPosition.x;
            userSubtitleOffsetY.value = targetY - initialPosition.y;

            console.log('📍 字幕位置设置完成:', {
                初始位置: initialPosition,
                目标位置: { x: targetX, y: targetY },
                偏移量: { x: userSubtitleOffsetX.value, y: userSubtitleOffsetY.value }
            });
        }

        // 🎯 处理尺寸配置
        if (subtitleConfigJson.width !== undefined && subtitleConfigJson.height !== undefined) {
            let targetWidth = parseFloat(subtitleConfigJson.width) || 380;
            let targetHeight = parseFloat(subtitleConfigJson.height) || 80;

            // 🔄 尺寸转换：如果是标准坐标，转换为页面尺寸
            if (isStandardCoord) {
                const pageSize = {
                    width: previewWindowWidth.value,
                    height: previewWindowHeight.value
                };

                const pageCoord = standardToPageCoord({
                    x: 0,  // 尺寸转换时位置设为0
                    y: 0,
                    width: targetWidth,
                    height: targetHeight
                }, pageSize);

                targetWidth = pageCoord.width;
                targetHeight = pageCoord.height;

                console.log('🔄 标准尺寸转换为页面尺寸:', {
                    标准尺寸: { width: subtitleConfigJson.width, height: subtitleConfigJson.height },
                    页面尺寸: { width: targetWidth, height: targetHeight }
                });
            }

            // 计算缩放比例（相对于页面坐标系默认尺寸的缩放）
            const defaultSize = getInitialSubtitleSize();  // 使用页面坐标系默认尺寸
            const defaultWidth = defaultSize.width;   // 基于页面窗口宽度的比例
            const defaultHeight = defaultSize.height; // 基于页面窗口高度的比例

            userSubtitleScaleX.value = targetWidth / defaultWidth;
            userSubtitleScaleY.value = targetHeight / defaultHeight;

            console.log('📏 字幕尺寸设置完成:', {
                默认尺寸: { width: defaultWidth, height: defaultHeight },
                目标尺寸: { width: targetWidth, height: targetHeight },
                缩放比例: { x: userSubtitleScaleX.value, y: userSubtitleScaleY.value }
            });
        }

        // 🔄 触发位置更新事件
        emitPositionUpdate();

        console.log('✅ 字幕配置设置完成');

    } catch (error) {
        console.error('❌ 设置字幕配置失败:', error);
    }
};

/**
 * 🏞️ 背景模块位置设置方法（用于数据回显）
 * 功能：根据保存的bgJson数据，恢复背景模块的位置和尺寸
 * 支持标准坐标系（1080×1920）和页面坐标系的自动识别和转换
 * @param {Object} bgJson - 背景配置数据
 * @param {boolean} isStandardCoord - 是否为标准坐标系数据，默认true
 */
const setBackgroundPosition = (bgJson, isStandardCoord = true) => {
    try {
        // 🔍 验证输入参数
        if (!bgJson || typeof bgJson !== 'object') {
            console.log('💡 没有有效的背景位置数据，跳过设置');
            return;
        }

        console.log('🏞️ 开始设置背景位置:', {
            输入数据: bgJson,
            坐标系类型: isStandardCoord ? `标准坐标(${STANDARD_SIZE.value.width}×${STANDARD_SIZE.value.height})` : '页面坐标'
        });

        // 🎯 处理位置配置
        if (bgJson.x !== undefined && bgJson.y !== undefined) {
            let targetX = validateCoordinate(parseFloat(bgJson.x) || 0);
            let targetY = validateCoordinate(parseFloat(bgJson.y) || 0);

            // 🔄 坐标系转换：如果是标准坐标，转换为页面坐标
            if (isStandardCoord) {
                const pageSize = {
                    width: previewWindowWidth.value,
                    height: previewWindowHeight.value
                };

                const pageCoord = standardToPageCoord({
                    x: targetX,
                    y: targetY,
                    width: 0,  // 位置转换时宽高设为0
                    height: 0
                }, pageSize);

                targetX = pageCoord.x;
                targetY = pageCoord.y;

                console.log('🔄 标准坐标转换为页面坐标:', {
                    标准坐标: { x: bgJson.x, y: bgJson.y },
                    页面坐标: { x: targetX, y: targetY },
                    页面尺寸: pageSize
                });
            }

            // 计算位置偏移量（相对于初始位置的偏移）
            const initialPosition = getInitialBackgroundModulePosition();
            userBackgroundModuleOffsetX.value = normalizeCoordinate(targetX - initialPosition.x);
            userBackgroundModuleOffsetY.value = normalizeCoordinate(targetY - initialPosition.y);

            console.log('📍 背景位置设置完成:', {
                初始位置: initialPosition,
                目标位置: { x: targetX, y: targetY },
                偏移量: { x: userBackgroundModuleOffsetX.value, y: userBackgroundModuleOffsetY.value }
            });
        }

        // 🎯 处理尺寸配置
        if (bgJson.width !== undefined && bgJson.height !== undefined) {
            let targetWidth = Math.max(BOUNDARY_CONFIG.minElementSize, parseFloat(bgJson.width) || 0);
            let targetHeight = Math.max(BOUNDARY_CONFIG.minElementSize, parseFloat(bgJson.height) || 0);

            // 🔄 尺寸转换：如果是标准坐标，转换为页面尺寸
            if (isStandardCoord) {
                const pageSize = {
                    width: previewWindowWidth.value,
                    height: previewWindowHeight.value
                };

                const pageCoord = standardToPageCoord({
                    x: 0,  // 尺寸转换时位置设为0
                    y: 0,
                    width: targetWidth,
                    height: targetHeight
                }, pageSize);

                targetWidth = pageCoord.width;
                targetHeight = pageCoord.height;

                console.log('🔄 标准尺寸转换为页面尺寸:', {
                    标准尺寸: { width: bgJson.width, height: bgJson.height },
                    页面尺寸: { width: targetWidth, height: targetHeight }
                });
            }

            // 计算缩放比例（相对于初始尺寸的缩放）
            const initialSize = getInitialBackgroundModuleSize();
            userBackgroundModuleScaleX.value = Math.max(0.1, targetWidth / initialSize.width);
            userBackgroundModuleScaleY.value = Math.max(0.1, targetHeight / initialSize.height);

            console.log('📏 背景尺寸设置完成:', {
                初始尺寸: initialSize,
                目标尺寸: { width: targetWidth, height: targetHeight },
                缩放比例: { x: userBackgroundModuleScaleX.value, y: userBackgroundModuleScaleY.value }
            });
        }

        // 🔄 触发位置更新事件
        emitPositionUpdate();

        console.log('✅ 背景位置设置完成');

    } catch (error) {
        console.error('❌ 设置背景位置失败:', error);
        // 重置为默认值，避免界面异常
        userBackgroundModuleOffsetX.value = 0;
        userBackgroundModuleOffsetY.value = 0;
        userBackgroundModuleScaleX.value = 1;
        userBackgroundModuleScaleY.value = 1;
    }
};

/**
 * 🧑‍🎨 数字人位置设置方法（用于数据回显）
 * 功能：根据保存的personJson数据，恢复数字人的位置和尺寸
 * 支持动态标准坐标系（9:16为1080×1920，16:9为1920×1080）和页面坐标系的自动识别和转换
 * @param {Object} personJson - 数字人配置数据
 * @param {boolean} isStandardCoord - 是否为标准坐标系数据，默认true
 */
const setCharacterPosition = (personJson, isStandardCoord = true) => {
    try {
        if (!personJson) {
            console.log('💡 没有数字人位置数据，跳过设置');
            return;
        }

        console.log('🧑‍🎨 开始设置数字人位置:', {
            输入数据: personJson,
            坐标系类型: isStandardCoord ? `标准坐标(${STANDARD_SIZE.value.width}×${STANDARD_SIZE.value.height})` : '页面坐标'
        });

        // 🎯 处理位置配置
        if (personJson.x !== undefined && personJson.y !== undefined) {
            let targetX = validateCoordinate(parseFloat(personJson.x) || 0);
            let targetY = validateCoordinate(parseFloat(personJson.y) || 0);

            // 🔄 坐标系转换：如果是标准坐标，转换为页面坐标
            if (isStandardCoord) {
                const pageSize = {
                    width: previewWindowWidth.value,
                    height: previewWindowHeight.value
                };

                const pageCoord = standardToPageCoord({
                    x: targetX,
                    y: targetY,
                    width: 0,  // 位置转换时宽高设为0
                    height: 0
                }, pageSize);

                targetX = pageCoord.x;
                targetY = pageCoord.y;

                console.log('🔄 标准坐标转换为页面坐标:', {
                    标准坐标: { x: personJson.x, y: personJson.y },
                    页面坐标: { x: targetX, y: targetY },
                    页面尺寸: pageSize
                });
            }

            // 计算位置偏移量（相对于初始位置的偏移）
            const initialPosition = getInitialCharacterPosition();
            userCharacterOffsetX.value = normalizeCoordinate(targetX - initialPosition.x);
            userCharacterOffsetY.value = normalizeCoordinate(targetY - initialPosition.y);

            console.log('📍 数字人位置设置完成:', {
                初始位置: initialPosition,
                目标位置: { x: targetX, y: targetY },
                偏移量: { x: userCharacterOffsetX.value, y: userCharacterOffsetY.value }
            });
        }

        // 🎯 处理尺寸配置
        if (personJson.width !== undefined && personJson.height !== undefined) {
            let targetWidth = Math.max(BOUNDARY_CONFIG.minElementSize, parseFloat(personJson.width) || 0);
            let targetHeight = Math.max(BOUNDARY_CONFIG.minElementSize, parseFloat(personJson.height) || 0);

            // 🔄 尺寸转换：如果是标准坐标，转换为页面尺寸
            if (isStandardCoord) {
                const pageSize = {
                    width: previewWindowWidth.value,
                    height: previewWindowHeight.value
                };

                const pageCoord = standardToPageCoord({
                    x: 0,  // 尺寸转换时位置设为0
                    y: 0,
                    width: targetWidth,
                    height: targetHeight
                }, pageSize);

                targetWidth = pageCoord.width;
                targetHeight = pageCoord.height;

                console.log('🔄 标准尺寸转换为页面尺寸:', {
                    标准尺寸: { width: personJson.width, height: personJson.height },
                    页面尺寸: { width: targetWidth, height: targetHeight }
                });
            }

            // 计算缩放比例（相对于初始尺寸的缩放）
            const initialSize = getInitialCharacterSize();
            userCharacterScaleX.value = Math.max(0.1, targetWidth / initialSize.width);
            userCharacterScaleY.value = Math.max(0.1, targetHeight / initialSize.height);

            console.log('📏 数字人尺寸设置完成:', {
                初始尺寸: initialSize,
                目标尺寸: { width: targetWidth, height: targetHeight },
                缩放比例: { x: userCharacterScaleX.value, y: userCharacterScaleY.value }
            });
        }

        // 🔄 触发位置更新事件
        emitPositionUpdate();

        console.log('✅ 数字人位置设置完成');

    } catch (error) {
        console.error('❌ 设置数字人位置失败:', error);
        // 重置为默认值，避免界面异常
        userCharacterOffsetX.value = 0;
        userCharacterOffsetY.value = 0;
        userCharacterScaleX.value = 1;
        userCharacterScaleY.value = 1;
    }
};

// ========================================
// 🔗 暴露给父组件的方法和数据
// ========================================

/**
 * 暴露给父组件的方法和数据
 * 用途：允许父组件主动获取位置数据，控制显示状态等
 *
 * 【位置数据获取方法说明】
 * - getAllPositionsData(): 获取所有元素的完整位置数据，返回包含x/y坐标、尺寸、状态等的对象
 * - getElementPosition(elementType): 获取指定元素的位置数据，elementType可为'character'/'secondImage'/'subtitle'
 * - emitPositionUpdate(): 手动触发位置更新事件，用于父组件强制刷新位置数据
 * - clearAllSelections(): 清除所有元素的选中状态，用于全局点击处理
 *
 * 【坐标系统】
 * - 所有x/y坐标均以预览窗口左上角为原点(0,0)
 * - x轴正方向向右，y轴正方向向下
 * - 所有坐标值单位均为像素(px)
 * - 坐标值可能为负数(表示元素部分位于预览窗口外)
 *
 * 【接口数据用途】
 * - 位置数据可直接传递给后端API保存
 * - 支持布局配置的导入导出
 * - 便于项目状态恢复和跨设备同步
 */
defineExpose({
    // 📊 位置数据获取方法（页面坐标系）
    getAllPositionsData,        // 获取所有元素位置数据（页面坐标）
    getElementPosition,         // 获取指定元素位置数据（页面坐标）
    emitPositionUpdate,         // 手动触发位置更新事件

    // 📊 位置数据获取方法（标准坐标系 - 1920×1080）
    getAllPositionsDataForAPI,  // 获取所有元素位置数据（标准坐标，供接口使用）
    getElementPositionForAPI,   // 获取指定元素位置数据（标准坐标，供接口使用）

    // 🔄 坐标转换工具方法
    pageToStandardCoord,        // 页面坐标转标准坐标
    standardToPageCoord,        // 标准坐标转页面坐标
    STANDARD_SIZE,              // 标准坐标系尺寸配置（动态：9:16为1080×1920，16:9为1920×1080）
    getStandardSize,            // 获取指定宽高比的标准尺寸函数
    testCoordinateConversion,   // 测试坐标转换逻辑

    // 📐 预览窗口尺寸信息
    previewWindowWidth,         // 当前预览窗口宽度
    previewWindowHeight,        // 当前预览窗口高度

    // 🎯 选中状态控制方法
    clearAllSelections,         // 清除所有元素选中状态
    // 🖱️ 右键菜单控制方法
    hideContextMenu,            // 隐藏右键菜单

    // ⌨️ 键盘删除控制方法
    handleKeyboardDelete,       // 键盘删除事件处理函数
    deleteSelectedElement,      // 删除选中的图层

    // 🎨 位置设置方法（支持双重坐标系）
    setBackgroundPosition,      // 设置背景模块位置（支持标准坐标和页面坐标）
    setCharacterPosition,       // 设置数字人位置（支持标准坐标和页面坐标）
    setSubtitleConfig,          // 设置字幕配置（支持标准坐标和页面坐标）

    // 🔧 调试和开发工具
    debugDragSystem,            // 调试统一拖拽系统状态

    // 🎭 元素显示控制方法
    toggleCharacter: () => { showCharacter.value = !showCharacter.value; },
    toggleSecondImage: () => { showSecondImage.value = !showSecondImage.value; },
    toggleSubtitle: () => { showSubtitle.value = !showSubtitle.value; },

    // 🎯 元素选择控制方法
    selectCharacter: () => {
        isCharacterActive.value = true;
        isSecondImageActive.value = false;
        isSubtitleActive.value = false;
    },
    selectSecondImage: () => {
        isSecondImageActive.value = true;
        isCharacterActive.value = false;
        isSubtitleActive.value = false;
    },
    selectSubtitle: () => {
        isSubtitleActive.value = true;
        isCharacterActive.value = false;
        isSecondImageActive.value = false;
    },
    deselectAll: () => {
        isCharacterActive.value = false;
        isSecondImageActive.value = false;
        isSubtitleActive.value = false;
    },

    // 📍 位置重置方法
    resetCharacterPosition: () => {
        userCharacterOffsetX.value = 0;
        userCharacterOffsetY.value = 0;
        userCharacterScaleX.value = 1;
        userCharacterScaleY.value = 1;
        emitPositionUpdate();
    },
    resetSecondImagePosition: () => {
        // 🎯 修复：重置统一的数字人数据
        userCharacterOffsetX.value = 0;
        userCharacterOffsetY.value = 0;
        userCharacterScaleX.value = 1;
        userCharacterScaleY.value = 1;
        emitPositionUpdate();
    },
    resetSubtitlePosition: () => {
        userSubtitleOffsetX.value = 0;
        userSubtitleOffsetY.value = 0;
        userSubtitleScaleX.value = 1;
        userSubtitleScaleY.value = 1;
        emitPositionUpdate();
    },
    resetAllPositions: () => {
        // 重置数字人角色
        userCharacterOffsetX.value = 0;
        userCharacterOffsetY.value = 0;
        userCharacterScaleX.value = 1;
        userCharacterScaleY.value = 1;

        // 重置装饰图片
        userSecondImageOffsetX.value = 0;
        userSecondImageOffsetY.value = 0;
        userSecondImageScaleX.value = 1;
        userSecondImageScaleY.value = 1;

        // 重置字幕
        userSubtitleOffsetX.value = 0;
        userSubtitleOffsetY.value = 0;
        userSubtitleScaleX.value = 1;
        userSubtitleScaleY.value = 1;

        emitPositionUpdate();
    }
});

// ========================================
// 🔄 拉伸功能实现
// ========================================

/**
 * 数字人角色拉伸开始 - 第一层
 * @param {string} direction - 拉伸方向 ('tl'|'tr'|'bl'|'br')
 * @param {MouseEvent} event - 鼠标事件
 */
const startCharacterResize = (direction, event) => {
    event.preventDefault();
    event.stopPropagation();

    isResizingCharacter = true;
    resizeDirection = direction;
    resizeStartX = event.clientX;
    resizeStartY = event.clientY;

    // 记录开始拉伸时的状态
    initialCharacterWidth = characterWidth.value;
    initialCharacterHeight = characterHeight.value;
    initialResizeCharacterX = characterX.value;
    initialResizeCharacterY = characterY.value;

    console.log('🔄 开始拉伸数字人（第一层）:', {
        方向: direction,
        初始尺寸: { width: initialCharacterWidth, height: initialCharacterHeight },
        初始位置: { x: initialResizeCharacterX, y: initialResizeCharacterY }
    });

    // 绑定全局事件
    document.addEventListener('mousemove', onCharacterResize);
    document.addEventListener('mouseup', stopCharacterResize);
};

/**
 * 数字人角色拉伸过程 - 第一层 (等比例缩放)
 * @param {MouseEvent} event - 鼠标事件
 */
const onCharacterResize = (event) => {
    if (!isResizingCharacter) return;

    const deltaX = event.clientX - resizeStartX;
    const deltaY = event.clientY - resizeStartY;

    // 计算等比例缩放：先计算新尺寸，再统一缩放
    let newWidth = initialCharacterWidth;
    let newHeight = initialCharacterHeight;
    
    // 根据拉伸方向计算新的尺寸
    switch (resizeDirection) {
        case 'tl': // 左上角
            newWidth = Math.max(50, initialCharacterWidth - deltaX);
            newHeight = Math.max(50, initialCharacterHeight - deltaY);
            break;
        case 'tr': // 右上角
            newWidth = Math.max(50, initialCharacterWidth + deltaX);
            newHeight = Math.max(50, initialCharacterHeight - deltaY);
            break;
        case 'bl': // 左下角
            newWidth = Math.max(50, initialCharacterWidth - deltaX);
            newHeight = Math.max(50, initialCharacterHeight + deltaY);
            break;
        case 'br': // 右下角
            newWidth = Math.max(50, initialCharacterWidth + deltaX);
            newHeight = Math.max(50, initialCharacterHeight + deltaY);
            break;
    }

    // 计算基于新尺寸的缩放比例，取变化更大的方向作为基准
    const initialSize = getInitialCharacterSize();
    const scaleX = newWidth / initialSize.width;
    const scaleY = newHeight / initialSize.height;
    
    // 选择变化更大的缩放比例作为统一缩放比例
    const newScale = Math.max(0.1, Math.max(scaleX, scaleY));
    
    // 只有右下角拉伸时不需要调整偏移，其他角需要根据缩放变化调整偏移
    if (resizeDirection === 'br') {
        // 右下角拉伸：只改变缩放，不改变偏移
        userCharacterScaleX.value = newScale;
        userCharacterScaleY.value = newScale;
    } else {
        // 其他角拉伸：需要调整偏移量以保持拉伸点固定
        const currentScale = userCharacterScaleX.value; // 当前缩放比例
        const scaleChange = newScale - currentScale;
        
        // 根据拉伸方向调整偏移
        switch (resizeDirection) {
            case 'tl': // 左上角：左边和上边需要调整
                userCharacterOffsetX.value += -scaleChange * initialSize.width;
                userCharacterOffsetY.value += -scaleChange * initialSize.height;
                break;
            case 'tr': // 右上角：只有上边需要调整
                userCharacterOffsetY.value += -scaleChange * initialSize.height;
                break;
            case 'bl': // 左下角：只有左边需要调整
                userCharacterOffsetX.value += -scaleChange * initialSize.width;
                break;
        }
        
        userCharacterScaleX.value = newScale;
        userCharacterScaleY.value = newScale;
    }
};

/**
 * 数字人角色拉伸结束 - 第一层
 */
const stopCharacterResize = () => {
    if (!isResizingCharacter) return;

    isResizingCharacter = false;
    
    // 解绑全局事件
    document.removeEventListener('mousemove', onCharacterResize);
    document.removeEventListener('mouseup', stopCharacterResize);

    // 发射位置更新事件
    emitPositionUpdate();
    
    console.log('✅ 数字人拉伸完成（第一层）');
};

/**
 * 数字人角色拉伸开始 - 第二层
 * @param {string} direction - 拉伸方向 ('tl'|'tr'|'bl'|'br')
 * @param {MouseEvent} event - 鼠标事件
 */
const startSecondImageResize = (direction, event) => {
    event.preventDefault();
    event.stopPropagation();

    isResizingSecondImage = true;
    secondResizeDirection = direction;
    secondResizeStartX = event.clientX;
    secondResizeStartY = event.clientY;

    // 记录开始拉伸时的状态（使用第二层数字人数据）
    initialSecondImageWidth = secondImageWidth.value;
    initialSecondImageHeight = secondImageHeight.value;
    initialResizeSecondImageX = secondImageX.value;
    initialResizeSecondImageY = secondImageY.value;

    console.log('🔄 开始拉伸数字人（第二层）:', {
        方向: direction,
        初始尺寸: { width: initialSecondImageWidth, height: initialSecondImageHeight },
        初始位置: { x: initialResizeSecondImageX, y: initialResizeSecondImageY }
    });

    // 绑定全局事件
    document.addEventListener('mousemove', onSecondImageResize);
    document.addEventListener('mouseup', stopSecondImageResize);
};

/**
 * 数字人角色拉伸过程 - 第二层 (等比例缩放)
 * @param {MouseEvent} event - 鼠标事件
 */
const onSecondImageResize = (event) => {
    if (!isResizingSecondImage) return;

    const deltaX = event.clientX - secondResizeStartX;
    const deltaY = event.clientY - secondResizeStartY;

    // 计算等比例缩放：先计算新尺寸，再统一缩放
    let newWidth = initialSecondImageWidth;
    let newHeight = initialSecondImageHeight;
    
    // 根据拉伸方向计算新的尺寸
    switch (secondResizeDirection) {
        case 'tl': // 左上角
            newWidth = Math.max(50, initialSecondImageWidth - deltaX);
            newHeight = Math.max(50, initialSecondImageHeight - deltaY);
            break;
        case 'tr': // 右上角
            newWidth = Math.max(50, initialSecondImageWidth + deltaX);
            newHeight = Math.max(50, initialSecondImageHeight - deltaY);
            break;
        case 'bl': // 左下角
            newWidth = Math.max(50, initialSecondImageWidth - deltaX);
            newHeight = Math.max(50, initialSecondImageHeight + deltaY);
            break;
        case 'br': // 右下角
            newWidth = Math.max(50, initialSecondImageWidth + deltaX);
            newHeight = Math.max(50, initialSecondImageHeight + deltaY);
            break;
    }

    // 计算基于新尺寸的缩放比例，取变化更大的方向作为基准
    const initialSize = getInitialSecondImageSize();
    const scaleX = newWidth / initialSize.width;
    const scaleY = newHeight / initialSize.height;
    
    // 选择变化更大的缩放比例作为统一缩放比例
    const newScale = Math.max(0.1, Math.max(scaleX, scaleY));
    
    // 只有右下角拉伸时不需要调整偏移，其他角需要根据缩放变化调整偏移
    if (secondResizeDirection === 'br') {
        // 右下角拉伸：只改变缩放，不改变偏移
        userCharacterScaleX.value = newScale;
        userCharacterScaleY.value = newScale;
    } else {
        // 其他角拉伸：需要调整偏移量以保持拉伸点固定
        const scaleChange = newScale - userCharacterScaleX.value;
        
        // 根据拉伸方向调整偏移
        switch (secondResizeDirection) {
            case 'tl': // 左上角：左边和上边需要调整
                userCharacterOffsetX.value += -scaleChange * initialSize.width;
                userCharacterOffsetY.value += -scaleChange * initialSize.height;
                break;
            case 'tr': // 右上角：只有上边需要调整
                userCharacterOffsetY.value += -scaleChange * initialSize.height;
                break;
            case 'bl': // 左下角：只有左边需要调整
                userCharacterOffsetX.value += -scaleChange * initialSize.width;
                break;
        }
        
        userCharacterScaleX.value = newScale;
        userCharacterScaleY.value = newScale;
    }
};

/**
 * 数字人角色拉伸结束 - 第二层
 */
const stopSecondImageResize = () => {
    if (!isResizingSecondImage) return;

    isResizingSecondImage = false;
    
    // 解绑全局事件
    document.removeEventListener('mousemove', onSecondImageResize);
    document.removeEventListener('mouseup', stopSecondImageResize);

    // 发射位置更新事件
    emitPositionUpdate();
    
    console.log('✅ 数字人拉伸完成（第二层）');
};

/**
 * 字幕拉伸开始
 * @param {string} direction - 拉伸方向 ('tl'|'tr'|'bl'|'br')
 * @param {MouseEvent} event - 鼠标事件
 */
const startSubtitleResize = (direction, event) => {
    event.preventDefault();
    event.stopPropagation();

    isResizingSubtitle = true;
    subtitleResizeDirection = direction;
    subtitleResizeStartX = event.clientX;
    subtitleResizeStartY = event.clientY;

    // 记录开始拉伸时的状态
    initialSubtitleWidth = subtitleWidth.value;
    initialSubtitleHeight = subtitleHeight.value;
    initialResizeSubtitleX = subtitleX.value;
    initialResizeSubtitleY = subtitleY.value;

    console.log('🔄 开始拉伸字幕:', {
        方向: direction,
        初始尺寸: { width: initialSubtitleWidth, height: initialSubtitleHeight },
        初始位置: { x: initialResizeSubtitleX, y: initialResizeSubtitleY }
    });

    // 绑定全局事件
    document.addEventListener('mousemove', onSubtitleResize);
    document.addEventListener('mouseup', stopSubtitleResize);
};

/**
 * 字幕拉伸过程 (修复偏移量计算)
 * @param {MouseEvent} event - 鼠标事件
 */
const onSubtitleResize = (event) => {
    if (!isResizingSubtitle) return;

    const deltaX = event.clientX - subtitleResizeStartX;
    const deltaY = event.clientY - subtitleResizeStartY;

    let newWidth = initialSubtitleWidth;
    let newHeight = initialSubtitleHeight;
    
    // 根据拉伸方向计算新的尺寸
    switch (subtitleResizeDirection) {
        case 'tl': // 左上角
            newWidth = Math.max(100, initialSubtitleWidth - deltaX);
            newHeight = Math.max(30, initialSubtitleHeight - deltaY);
            break;
        case 'tr': // 右上角
            newWidth = Math.max(100, initialSubtitleWidth + deltaX);
            newHeight = Math.max(30, initialSubtitleHeight - deltaY);
            break;
        case 'bl': // 左下角
            newWidth = Math.max(100, initialSubtitleWidth - deltaX);
            newHeight = Math.max(30, initialSubtitleHeight + deltaY);
            break;
        case 'br': // 右下角
            newWidth = Math.max(100, initialSubtitleWidth + deltaX);
            newHeight = Math.max(30, initialSubtitleHeight + deltaY);
            break;
    }

    // 计算新的缩放比例
    const initialSize = getInitialSubtitleSize();
    const newScaleX = Math.max(0.1, newWidth / initialSize.width);
    const newScaleY = Math.max(0.1, newHeight / initialSize.height);
    
    // 只有右下角拉伸时不需要调整偏移，其他角需要根据缩放变化调整偏移
    if (subtitleResizeDirection === 'br') {
        // 右下角拉伸：只改变缩放，不改变偏移
        userSubtitleScaleX.value = newScaleX;
        userSubtitleScaleY.value = newScaleY;
    } else {
        // 其他角拉伸：需要调整偏移量以保持拉伸点固定
        const scaleChangeX = newScaleX - userSubtitleScaleX.value;
        const scaleChangeY = newScaleY - userSubtitleScaleY.value;
        
        // 根据拉伸方向调整偏移
        switch (subtitleResizeDirection) {
            case 'tl': // 左上角：左边和上边需要调整
                userSubtitleOffsetX.value += -scaleChangeX * initialSize.width;
                userSubtitleOffsetY.value += -scaleChangeY * initialSize.height;
                break;
            case 'tr': // 右上角：只有上边需要调整
                userSubtitleOffsetY.value += -scaleChangeY * initialSize.height;
                break;
            case 'bl': // 左下角：只有左边需要调整
                userSubtitleOffsetX.value += -scaleChangeX * initialSize.width;
                break;
        }
        
        userSubtitleScaleX.value = newScaleX;
        userSubtitleScaleY.value = newScaleY;
    }
};

/**
 * 字幕拉伸结束
 */
const stopSubtitleResize = () => {
    if (!isResizingSubtitle) return;

    isResizingSubtitle = false;
    
    // 解绑全局事件
    document.removeEventListener('mousemove', onSubtitleResize);
    document.removeEventListener('mouseup', stopSubtitleResize);

    // 发射位置更新事件
    emitPositionUpdate();
    
    console.log('✅ 字幕拉伸完成');
};

/**
 * 背景模块拉伸开始
 * @param {string} direction - 拉伸方向 ('tl'|'tr'|'bl'|'br')
 * @param {MouseEvent} event - 鼠标事件
 */
const startBackgroundModuleResize = (direction, event) => {
    event.preventDefault();
    event.stopPropagation();

    // 检查是否为纯色背景，纯色背景不支持拉伸
    if (props.backgroundConfig && props.backgroundConfig.type === 'color') {
        console.log('🚫 纯色背景不支持拉伸');
        return;
    }

    isResizingBackgroundModule = true;
    backgroundResizeDirection = direction;
    backgroundResizeStartX = event.clientX;
    backgroundResizeStartY = event.clientY;

    // 记录开始拉伸时的状态
    initialBackgroundWidth = backgroundModuleWidth.value;
    initialBackgroundHeight = backgroundModuleHeight.value;
    initialResizeBackgroundX = backgroundModuleX.value;
    initialResizeBackgroundY = backgroundModuleY.value;

    console.log('🔄 开始拉伸背景模块:', {
        方向: direction,
        初始尺寸: { width: initialBackgroundWidth, height: initialBackgroundHeight },
        初始位置: { x: initialResizeBackgroundX, y: initialResizeBackgroundY }
    });

    // 绑定全局事件
    document.addEventListener('mousemove', onBackgroundModuleResize);
    document.addEventListener('mouseup', stopBackgroundModuleResize);
};

/**
 * 背景模块拉伸过程 (等比例缩放)
 * @param {MouseEvent} event - 鼠标事件
 */
const onBackgroundModuleResize = (event) => {
    if (!isResizingBackgroundModule) return;

    const deltaX = event.clientX - backgroundResizeStartX;
    const deltaY = event.clientY - backgroundResizeStartY;

    // 计算等比例缩放：先计算新尺寸，再统一缩放
    let newWidth = initialBackgroundWidth;
    let newHeight = initialBackgroundHeight;
    
    // 根据拉伸方向计算新的尺寸
    switch (backgroundResizeDirection) {
        case 'tl': // 左上角
            newWidth = Math.max(100, initialBackgroundWidth - deltaX);
            newHeight = Math.max(100, initialBackgroundHeight - deltaY);
            break;
        case 'tr': // 右上角
            newWidth = Math.max(100, initialBackgroundWidth + deltaX);
            newHeight = Math.max(100, initialBackgroundHeight - deltaY);
            break;
        case 'bl': // 左下角
            newWidth = Math.max(100, initialBackgroundWidth - deltaX);
            newHeight = Math.max(100, initialBackgroundHeight + deltaY);
            break;
        case 'br': // 右下角
            newWidth = Math.max(100, initialBackgroundWidth + deltaX);
            newHeight = Math.max(100, initialBackgroundHeight + deltaY);
            break;
    }

    // 计算基于新尺寸的缩放比例，取变化更大的方向作为基准
    const initialSize = getInitialBackgroundModuleSize();
    const scaleX = newWidth / initialSize.width;
    const scaleY = newHeight / initialSize.height;
    
    // 选择变化更大的缩放比例作为统一缩放比例
    const newScale = Math.max(0.1, Math.max(scaleX, scaleY));
    
    // 只有右下角拉伸时不需要调整偏移，其他角需要根据缩放变化调整偏移
    if (backgroundResizeDirection === 'br') {
        // 右下角拉伸：只改变缩放，不改变偏移
        userBackgroundModuleScaleX.value = newScale;
        userBackgroundModuleScaleY.value = newScale;
    } else {
        // 其他角拉伸：需要调整偏移量以保持拉伸点固定
        const scaleChange = newScale - userBackgroundModuleScaleX.value;
        
        // 根据拉伸方向调整偏移
        switch (backgroundResizeDirection) {
            case 'tl': // 左上角：左边和上边需要调整
                userBackgroundModuleOffsetX.value += -scaleChange * initialSize.width;
                userBackgroundModuleOffsetY.value += -scaleChange * initialSize.height;
                break;
            case 'tr': // 右上角：只有上边需要调整
                userBackgroundModuleOffsetY.value += -scaleChange * initialSize.height;
                break;
            case 'bl': // 左下角：只有左边需要调整
                userBackgroundModuleOffsetX.value += -scaleChange * initialSize.width;
                break;
        }
        
        userBackgroundModuleScaleX.value = newScale;
        userBackgroundModuleScaleY.value = newScale;
    }
};

/**
 * 背景模块拉伸结束
 */
const stopBackgroundModuleResize = () => {
    if (!isResizingBackgroundModule) return;

    isResizingBackgroundModule = false;
    
    // 解绑全局事件
    document.removeEventListener('mousemove', onBackgroundModuleResize);
    document.removeEventListener('mouseup', stopBackgroundModuleResize);

    // 发射位置更新事件
    emitPositionUpdate();
    
    console.log('✅ 背景模块拉伸完成');
};

// 添加背景模块拉伸相关变量
let isResizingBackgroundModule = false;
let backgroundResizeDirection = '';
let backgroundResizeStartX = 0;
let backgroundResizeStartY = 0;
let initialBackgroundWidth = 0;
let initialBackgroundHeight = 0;
let initialResizeBackgroundX = 0;
let initialResizeBackgroundY = 0;
</script>

<style scoped lang="scss">
/* ========================================
   🎨 主容器布局样式
   ======================================== */

/**
 * 预览编辑器主容器
 * 布局策略：垂直居中的弹性布局
 * 溢出处理：visible确保选择框和控制点可见
 */
.preview-editor-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
    position: relative;
    overflow: visible; // 确保超出的选择框可见
}

/* ========================================
   🖱️ 右键菜单样式
   ======================================== */

/**
 * 右键菜单容器
 * 设计理念：现代化卡片式菜单，提供直观的删除操作
 */
.context-menu {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(224, 224, 224, 0.8);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
    padding: 4px 0;
    min-width: 120px;
    user-select: none;
    backdrop-filter: blur(16px);
    z-index: 1000;

    /* 动态效果 */
    animation: contextMenuFadeIn 0.2s ease-out;
    transform-origin: top left;
}

/* 右键菜单淡入动画 */
@keyframes contextMenuFadeIn {
    0% {
        opacity: 0;
        transform: scale(0.8) translateY(-8px);
    }

    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/**
 * 菜单项样式
 * 交互特点：悬停高亮、平滑过渡、黑色主题
 */
.context-menu-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 8px;
    font-size: 14px;
    color: #333; // 删除操作使用黑色文字
    line-height: 1;

    &:hover {
        background-color: #f5f5f5;
    }

    &:active {
        background-color: #e8e8e8;
    }
}

/**
 * 删除图标样式 (Element Plus)
 */
.delete-icon {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.2s ease;
}

.context-menu-item:hover .delete-icon {
    opacity: 0.8;
}

/**
 * 菜单项文字样式
 */
.context-menu-item span {
    display: flex;
    align-items: center;
    line-height: 1;
}

/* ========================================
   🎯 选择容器系统
   ======================================== */

/**
 * 外层选择框容器
 * 核心功能：扩展操作区域，支持元素拖拽到预览窗口外
 * 交互策略：默认禁用指针事件，仅选择框组件可交互
 */
.selection-container {
    pointer-events: none; // 默认禁用指针事件
    overflow: visible; // 确保超出部分可见

    // 只有选择框组件本身可以接收指针事件
    .character-selection-box,
    .subtitle-selection-box {
        pointer-events: auto;
        overflow: visible; // 确保手柄在超出时仍可见
    }
}

/* ========================================
   📱 预览窗口核心样式
   ======================================== */

/**
 * 预览窗口主体
 * 设计理念：
 * - 圆角边框增强现代感
 * - 轻微阴影提升视觉层次
 * - 高层级确保能接收点击事件
 * - 可见溢出让边框显示在外部
 */
.preview-window {
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    margin-top: 36px;
    // 背景色现在通过内联样式动态设置，移除硬编码的白色背景
    box-sizing: border-box;
    overflow: visible; // 改为visible，让边框可以显示在预览区外
    position: relative;
    z-index: 100; // 确保预览窗口能接收点击事件
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); // 添加轻微阴影增强视觉效果
}

/**
 * 内容裁剪层
 * 功能：裁剪图片内容但不影响边框和控制点
 * 层级：在背景层，低于所有交互元素
 * 定位：作为绝对定位子元素的定位参考点
 */
.content-clip-layer {
    position: relative; // 作为绝对定位子元素的定位参考点
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden; // 裁剪图片内容
    border-radius: 10px; // 与预览窗口的圆角保持一致
    z-index: 1; // 在背景层，低于边框
}

/* ========================================
   🎭 图像显示系统样式
   ======================================== */

/**
 * 文本覆盖层基础样式
 * 布局：铺满整个预览窗口
 */
.text-overlay-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
}

/**
 * 数字人角色边框容器样式
 * 交互设计：
 * - 禁用过渡效果确保实时跟随
 * - 禁用文本选择避免干扰
 * - 相对定位为拉伸手柄提供基准
 * - 中等层级确保在裁剪层之上
 */
.character-display {
    transition: none; // 禁用过渡效果，确保实时跟随
    user-select: none; // 禁用文本选择
    position: relative; // 为拉伸手柄提供定位基准
    z-index: 10; // 确保在裁剪层之上，边框可见
    border: 2px solid transparent; // 默认透明边框
    cursor: move; // 移动光标

    &.active {
        border-color: #4CAF50; // 绿色边框表示选中
        box-shadow: 0 0 0 1px rgba(76, 175, 80, 0.3);
    }

    &.hovering {
        border-color: rgba(76, 175, 80, 0.6); // 半透明绿色边框表示悬停
    }

    /* 播放时的样式：隐藏边框和阴影，禁用交互 */
    &.playing {
        border-color: transparent !important;
        box-shadow: none !important;
        cursor: default;
        pointer-events: none;

        &:hover {
            border-color: transparent !important;
        }
    }
}

/**
 * 数字人角色内容样式
 * 支持图片和纯色两种模式
 */
.character-image-content {
    .character-layer {
        width: 100%;
        height: 100%;
        /* object-fit 现在通过内联样式动态设置 */
        border-radius: 8px;
        box-sizing: border-box;
    }

    .character-color-layer {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        box-sizing: border-box;
    }
}

/**
 * 第二个图片边框容器样式
 * 层级：高于数字人角色，确保在重叠时能被选中
 */
.second-image-display {
    transition: none; // 禁用过渡效果，确保实时跟随
    user-select: none; // 禁用文本选择
    position: relative; // 为拉伸手柄提供定位基准
    z-index: 15; // 确保在数字人角色之上，边框可见
    border: 2px solid transparent; // 默认透明边框
    cursor: move; // 移动光标

    &.active {
        border-color: #4CAF50; // 绿色边框表示选中
        box-shadow: 0 0 0 1px rgba(76, 175, 80, 0.3);
    }

    &.hovering {
        border-color: rgba(76, 175, 80, 0.6); // 半透明绿色边框表示悬停
    }

    /* 播放时的样式：隐藏边框和阴影，禁用交互 */
    &.playing {
        border-color: transparent !important;
        box-shadow: none !important;
        cursor: default;
        pointer-events: none;

        &:hover {
            border-color: transparent !important;
        }
    }
}

/**
 * 第二个图片内容样式
 */
.second-image-content {
    .second-image-layer {
        width: 100%;
        height: 100%;
        /* object-fit 现在通过内联样式动态设置 */
        border-radius: 8px;
        box-sizing: border-box;
    }
}

/**
 * 背景模块边框容器样式
 * 层级：在背景层之上，数字人角色之下
 */
.background-module-display {
    transition: none; // 禁用过渡效果，确保实时跟随
    user-select: none; // 禁用文本选择
    position: relative; // 为拉伸手柄提供定位基准
    z-index: 8; // 在背景层之上，数字人角色之下
    border: 2px solid transparent; // 默认透明边框
    cursor: move; // 移动光标

    &.active {
        border-color: #FF9800; // 橙色边框表示选中
        box-shadow: 0 0 0 1px rgba(255, 152, 0, 0.3);
    }

    &.hovering {
        border-color: rgba(255, 152, 0, 0.6); // 半透明橙色边框表示悬停
    }

    /* 播放时的样式：隐藏边框和阴影，禁用交互 */
    &.playing {
        border-color: transparent !important;
        box-shadow: none !important;
        cursor: default;
        pointer-events: none;

        &:hover {
            border-color: transparent !important;
        }
    }
}

/**
 * 背景模块内容样式
 */
.background-module-content {
    .background-module-layer {
        width: 100%;
        height: 100%;
        border-radius: 8px;
        box-sizing: border-box;
    }
}

/* ========================================
   📝 字幕显示系统样式
   ======================================== */

/**
 * 字幕内容样式（在裁剪层内）
 * 功能特色：
 * - 显示字幕文本内容
 * - 支持文本样式和描边效果
 * - 支持最多三行自动换行
 */
.subtitle-content {
    position: relative;
    user-select: none;
    /* 移除单行限制，允许多行显示，具体行数限制由JavaScript控制 */
}

/**
 * 字幕边框容器样式（在裁剪层外）
 * 功能特色：
 * - 与背景层和数字人角色层保持一致的交互结构
 * - 相对定位支持拉伸手柄
 * - 平滑过渡提升视觉质量
 * - 无阴影效果，只有边框
 * - 边框和悬停样式由计算属性控制
 */
.subtitle-display {
    position: relative; // 为拉伸手柄提供定位基准
    transition: all 0.2s ease;
    user-select: none;
    z-index: 20; // 确保在其他元素之上，边框可见
    border: 2px solid transparent; // 默认透明边框
    cursor: move; // 移动光标

    &.active {
        border-color: #2196F3; // 蓝色边框表示字幕选中
        box-shadow: 0 0 0 1px rgba(33, 150, 243, 0.3);
    }

    &.hovering {
        border-color: rgba(33, 150, 243, 0.6); // 半透明蓝色边框表示悬停
    }

    /* 播放时的样式：隐藏边框，禁用交互 */
    &.playing {
        border-color: transparent !important;
        box-shadow: none !important;
        cursor: default !important;
        pointer-events: none;

        &:hover {
            border-color: transparent !important;
        }
    }
}

/* ========================================
   🔧 拉伸控制系统样式
   ======================================== */

/**
 * 拉伸手柄容器
 * 布局策略：绝对定位覆盖整个父容器
 * 交互策略：默认不接收事件，仅手柄可交互
 */
.resize-handles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none; // 默认不接收事件
    z-index: 200; // 确保在图片之上
}

/**
 * 拉伸手柄基础样式
 * 设计理念：
 * - 圆形设计更符合用户直觉
 * - 绿色主题与选中状态保持一致
 * - 白色边框增强对比度
 * - 悬停效果提供操作反馈
 */
.resize-handle {
    position: absolute;
    width: 10px;
    height: 10px;
    background-color: #4CAF50;
    border: 1px solid #fff;
    border-radius: 50%;
    pointer-events: auto; // 手柄可以接收事件
    z-index: 201;

    &:hover {
        background-color: #45a049;
    }
}

/* ========================================
   🧭 拉伸手柄位置定义
   ======================================== */

/**
 * 四角拉伸手柄位置和光标样式
 * 布局：在容器四个角落，向外偏移5px
 * 光标：对应方向的resize光标
 */
.resize-handle-tl {
    top: -5px;
    left: -5px;
    cursor: nw-resize; // 左上角拉伸光标
}

.resize-handle-tr {
    top: -5px;
    right: -5px;
    cursor: ne-resize; // 右上角拉伸光标
}

.resize-handle-bl {
    bottom: -5px;
    left: -5px;
    cursor: sw-resize; // 左下角拉伸光标
}

.resize-handle-br {
    bottom: -5px;
    right: -5px;
    cursor: se-resize; // 右下角拉伸光标
}

/**
 * 字幕专用拉伸手柄位置
 * 布局：左右各三个控制点（上中下）
 * 功能：支持水平和斜向拉伸
 */

// 左侧三个手柄：上中下
.resize-handle-lt {
    top: 0;
    left: -5px;
    transform: translateY(-50%);
    cursor: nw-resize; // 左上角斜向拉伸
}

.resize-handle-lm {
    top: 50%;
    left: -5px;
    transform: translateY(-50%);
    cursor: ew-resize; // 水平拉伸
}

.resize-handle-lb {
    top: 100%;
    left: -5px;
    transform: translateY(-50%);
    cursor: sw-resize; // 左下角斜向拉伸
}

// 右侧三个手柄：上中下
.resize-handle-rt {
    top: 0;
    right: -5px;
    transform: translateY(-50%);
    cursor: ne-resize; // 右上角斜向拉伸
}

.resize-handle-rm {
    top: 50%;
    right: -5px;
    transform: translateY(-50%);
    cursor: ew-resize; // 水平拉伸
}

.resize-handle-rb {
    top: 100%;
    right: -5px;
    transform: translateY(-50%);
    cursor: se-resize; // 右下角斜向拉伸
}

/* ========================================
   📺 时间轴文本覆盖样式
   ======================================== */

/**
 * 时间轴文本覆盖层
 * 功能：显示基于时间轴事件的动态文本
 * 样式：居中显示，半透明背景，优化字体渲染
 */
.text-overlay-layer {
    z-index: 10;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background-color: rgba(0, 0, 0, 0.5);
    text-rendering: optimizeLegibility; // 优化文本渲染
    -webkit-font-smoothing: antialiased; // 抗锯齿字体平滑
    pointer-events: none; // 不阻挡下层交互
}

/* ========================================
   🎮 控制按钮样式
   ======================================== */

/**
 * 控制按钮容器
 * 布局：水平排列，间距统一
 */
.controls {
    margin-bottom: 10px;
    display: flex;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
}

/**
 * 播放控制图片按钮
 * 交互：悬停透明度变化提供反馈
 */
.play-control-image {
    cursor: pointer;
    width: auto;
    height: auto;
    transition: opacity 0.2s ease;
    /* 居中显示时不需要额外左边距 */

    &:hover {
        opacity: 0.8;
    }
}

/**
 * 右下角时间显示样式
 * 功能：显示当前播放时间和总时长
 * 布局：定位在容器右下角
 */
.time-display-corner {
    position: absolute;
    bottom: 20px;
    right: 20px;
    color: #666;
    font-size: 14px;
    background-color: rgba(255, 255, 255, 0.9);
    padding: 4px 8px;
    border-radius: 4px;
    user-select: none;
    white-space: nowrap;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 100;
}

/* 居中播放按钮 */
.controls {
    margin-bottom: 10px;
    display: flex;
    justify-content: center;
    /* 水平居中 */
    align-items: center;
}





.lip-sync-alert {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 712px;
    height: 53px;
    background-color: #FFFFFF;
    border-radius: 8px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    z-index: 200;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    box-sizing: border-box;
    border: 1px solid #E5E6EB;

    .alert-icon {
        width: 24px;
        height: 24px;
        margin-right: 12px;
        flex-shrink: 0;
    }

    .alert-text {
        font-size: 14px;
        color: #353D49;
        line-height: 1.5;
        flex-grow: 1;
    }

    .alert-countdown {
        font-size: 12px;
        color: #999;
        margin-left: 12px;
        white-space: nowrap;
        flex-shrink: 0;
    }

    .alert-dismiss {
        font-size: 14px;
        color: #FF3B30;
        cursor: pointer;
        margin-left: 20px;
        white-space: nowrap;
        flex-shrink: 0;

        &:hover {
            opacity: 0.8;
        }
    }
}
</style>